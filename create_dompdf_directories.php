<?php

/**
 * Simple script to create DomPDF directories with proper permissions
 * Run this script directly: php create_dompdf_directories.php
 */

// Define the base path
$basePath = __DIR__;

// Define directories to create
$directories = [
    $basePath . '/storage/fonts',
    $basePath . '/storage/app/pdf',
    $basePath . '/storage/app/dompdf',
    $basePath . '/storage/app/dompdf/fonts',
];

// Create directories and set permissions
foreach ($directories as $directory) {
    if (!file_exists($directory)) {
        echo "Creating directory: {$directory}\n";
        
        if (mkdir($directory, 0755, true)) {
            echo "Directory created successfully.\n";
        } else {
            echo "Failed to create directory.\n";
        }
    } else {
        echo "Directory already exists: {$directory}\n";
    }
    
    // Set permissions
    echo "Setting permissions for: {$directory}\n";
    chmod($directory, 0755);
}

echo "All directories created and permissions set.\n";
echo "Now run the following command to set proper ownership:\n";
echo "chown -R www-data:www-data " . $basePath . "/storage\n";
echo "(Replace www-data with your web server user if different)\n";
