# PayStrix Payment Gateway Integration

This document explains how to set up and use the PayStrix payment gateway integration based on Boxcoin Payment Script.

## Overview

PayStrix is a payment gateway that allows customers to pay using various cryptocurrencies. The integration uses URL-based checkout with query parameters to initiate payments and receives webhook notifications when payments are completed.

## Configuration

To configure the PayStrix gateway, follow these steps:

1. Log in to your admin panel
2. Navigate to Payment Gateways
3. Find and edit the PayStrix gateway
4. Configure the following parameters:
   - **Base URL**: The base URL for the PayStrix checkout (e.g., `https://paystrix.com/checkout`)
   - **Webhook Key**: The secret key used to verify webhook signatures

## How It Works

### Checkout Process

When a customer initiates a payment:

1. The system generates a checkout URL with the following parameters:
   - `checkout_id`: A unique ID for the checkout (prefixed with "custom-")
   - `price`: The payment amount
   - `external-reference`: Your internal transaction reference
   - `redirect`: URL to redirect after payment
   - `currency`: The payment currency
   - `type`: Checkout type (inline, link, popup, hidden)
   - `note`: A note about the transaction

2. The customer is redirected to the PayStrix checkout page
3. After completing payment, the customer is redirected back to your success page

### Webhook Processing

When a payment is completed, PayStrix sends a webhook notification to your system with the following data:

```json
{
    "key": "123456789",
    "transaction": {
        "id": "231",
        "title": "My checkout title",
        "description": "My checkout description",
        "from": "******************************************",
        "to": "******************************************",
        "hash": "0x3c437c3245999087d8e0276e73f9411f0e593ccd5ed1feacb032724a8980a606",
        "amount": "0.001",
        "amount_fiat": "0.1",
        "cryptocurrency": "eth",
        "currency": "eur",
        "external_reference": "",
        "creation_time": "2022-04-14 10:00:00",
        "status": "C",
        "webhook": "1"
        "vat": {"amount":"0.33","percentage":"22","country":"Italy","country_code":"IT"}
    }
}
```

The system verifies the payment details and updates the order status accordingly.

## Testing

To test the PayStrix integration:

1. Set up the gateway with test credentials
2. Make a test payment
3. Verify that the webhook is received and processed correctly

## Troubleshooting

If you encounter issues with the PayStrix integration:

1. Check the webhook URL is correctly configured in your PayStrix account
2. Verify that the webhook key is correctly set in your gateway settings
3. Check the server logs for any errors during webhook processing
4. Ensure that your server can receive external webhook requests

## Support

For additional support with the PayStrix integration, please contact your PayStrix account manager or the system administrator.
