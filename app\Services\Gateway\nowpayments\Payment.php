<?php

namespace App\Services\Gateway\nowpayments;

use App\Models\Fund;
use Facades\App\Services\BasicService;
use Illuminate\Support\Facades\Log;

class Payment
{
    public static function prepareData($order, $gateway)
    {
        $APIkey = $gateway->parameters->api_key ?? '';
        $url = 'https://api.nowpayments.io/v1/';
//        $url = 'https://api-sandbox.nowpayments.io/v1/';

    // if we already created a payment_id and it's still “waiting”, re‑use it
    if ($order->payment_id) {
        $existing = self::getNowPayment($order->payment_id, $APIkey);
        if (!empty($existing['payment_status'])
            && $existing['payment_status'] === 'waiting'
            && (float)$existing['pay_amount'] > 0
        ) 
            {
                // rebuild exactly what you return below
                $order->btc_wallet = $existing['pay_address'];
                $order->btc_amount = ceil($existing['pay_amount']);
                // don’t touch payment_id or overwrite it
                $send = [
                    'amount'   => $order->btc_amount,
                    'sendto'   => $order->btc_wallet,
                    'img'      => BasicService::cryptoQR($order->btc_wallet, $order->btc_amount, null, false),
                    'currency' => $order->gateway_currency,
                    'view'     => 'payment.crypto',
                ];
                return json_encode($send);
            }    
        }
        $postField['price_amount'] = (string)round($order->final_amount, 2);
        $postField['price_currency'] = "USD";
        $postField['pay_currency'] = $order->gateway_currency;
        $postField['ipn_callback_url'] = "https://nestblock.net/payment/nowpayments";//"https://nowpayments.io";
        $postField['order_id'] = $order->transaction;
        $postField['order_description'] = "Deposit on " . config('basic.site_title') . " account";


        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url . 'payment',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($postField),
            CURLOPT_HTTPHEADER => array(
                "x-api-key: $APIkey",
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        $result = json_decode($response);
        if (isset($result->status) && $result->status == false) {
            $send['error'] = true;
            $send['message'] = $result->message;
            $send['view'] = 'payment.crypto';
            return json_encode($send);
        } else {
            $order['btc_wallet'] = $result->pay_address;
            $order['btc_amount'] =  ceil($result->pay_amount);//$result->pay_amount;
            $order['payment_id'] = $result->payment_id;
            $order->update();
        }


        $send['amount'] = $order->btc_amount;
        $send['sendto'] = $order->btc_wallet;
        $send['img'] = BasicService::cryptoQR($order->btc_wallet, $order->btc_amount, null, false); // Set includeAmount to false
        $send['currency'] = $order->gateway_currency ?? 'BTC';
        $send['view'] = 'payment.crypto';
        return json_encode($send);
    }


    public static function ipn($request, $gateway, $order = null, $trx = null, $type = null)
    {
		Log::info($request);
        $APIkey = $gateway->parameters->api_key ?? '';
        $url = 'https://api.nowpayments.io/v1/';
//        $url = 'https://api-sandbox.nowpayments.io/v1/';
		$orderId = $request['order_id'];
        $paymentId = $request['payment_id'];

        $orderData = Fund::with('gateway')
            ->whereHas('gateway', function ($query) {
                $query->where('code', 'nowpayments');
            })
            ->where('status', 0)
            ->where('payment_id', $paymentId)
			->where('transaction', $orderId)
            ->first();

        
        if (!$orderData) {
                // no match → ignore or log
                return;
            }

        $res = self::getNowPayment($paymentId, $APIkey);

        if (isset($res->payment_status) && (( $res->payment_status == 'confirmed' ) 
        || ($res->payment_status == 'sending')
        || ($res->payment_status == 'finished')
        ) && ($res->actually_paid >= $res->price_amount) ) {
            BasicService::preparePaymentUpgradation($orderData);
        }
    }

    protected static function getNowPayment($paymentId, $apiKey)
    {
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL            => "https://api.nowpayments.io/v1/payment/{$paymentId}",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER     => ["x-api-key: {$apiKey}"],
        ]);
        $resp = curl_exec($curl);
        curl_close($curl);
        return json_decode($resp, true);
    }
}
