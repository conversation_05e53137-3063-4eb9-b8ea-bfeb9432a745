<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Traits\Upload;
use App\Models\Country;
use App\Models\CountryService;
use App\Models\Coupon;
use App\Models\IdentifyForm;
use App\Models\KYC;
use App\Models\Language;
use App\Models\SendingPurpose;
use App\Models\SendMoney;
use App\Models\SourceFund;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use <PERSON>bauman\Purify\Facades\Purify;
use Facades\App\Services\Flutterwave;

class BasicApiController extends Controller
{
    use Upload;

    public function currencyList()
    {
        $result['status'] = true;
        $result['message'] = null;
        $data['senderCurrencies'] = Country::select('id', 'name', 'slug', 'code', 'minimum_amount', 'rate', 'facilities', 'image')->where('send_from', 1)->where('status', 1)
            //->orderByRaw("FIELD(short_code,'US') DESC")
            ->orderBy('name')->get()->map(function ($item) {
                $item->minimum_amount = (float)$item->minimum_amount;
                $item->rate = (float)$item->rate;

                return $item;

            });
        $data['receiverCurrencies'] = Country::select('id', 'name', 'slug', 'code', 'minimum_amount', 'rate', 'facilities', 'image')->where('send_to', 1)->where('status', 1)
            //->orderByRaw("FIELD(short_code,'NG') DESC")
            ->orderBy('name')->get()->map(function ($item) {
                $item->minimum_amount = (float)$item->minimum_amount;
                $item->rate = (float)$item->rate;
                return $item;
            });
        $result['data'] = $data;
        return response($result, 200);
    }

    public function toCountry($id)
    {
        $country = Country::where([
            'id' => $id,
            'status' => 1,
            'send_to' => 1,
        ])->first();
        if (!$country) {
            $result['status'] = false;
            $result['message'] = 'Receiver country not found';
            $result['data'] = [];
            return response($result, 200);
        }


        $country->minimum_amount = (float)$country->minimum_amount;
        $country->rate = (float)$country->rate;


        $data['page_title'] = "Send Money To $country->name";
        $data['selected_country'] = $country;
        $data['otherReceiveCountryList'] = Country::select('id', 'name', 'slug', 'code', 'minimum_amount', 'rate', 'facilities', 'image')->where('status', 1)->where('send_to', 1)
            //->orderByRaw("FIELD(short_code,'NG') DESC")
            ->orderBy('name')->get()->map(function ($item) {
                $item->minimum_amount = (float)$item->minimum_amount;
                $item->rate = (float)$item->rate;
                return $item;
            });


        $result['status'] = true;
        $result['message'] = null;
        $result['data'] = $data;
        return response($result, 200);
    }

    public function countryService(Request $request)
    {
        $validate = validator()->make($request->all(), [
            'countryId' => 'required',
            'serviceId' => 'required'
        ]);
        if ($validate->fails()) {
            return response()->json(['errors' => $validate->messages()], 422);
        }
        $data['providers'] = CountryService::select('id', 'name', 'bank_code', 'services_form')->where('country_id', $request->countryId)->where('service_id', $request->serviceId)->where('status', 1)->get();
        $result['status'] = true;
        $result['data'] = $data;
        return response($result, 200);
    }

    public function moneyCalculation(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'amount' => 'required|numeric',
            'sendCountry' => 'required|numeric',
            'getCountry' => 'required|numeric',
            'serviceId' => 'required|numeric',
            'sendReceive' => ['required', Rule::in(["send", "receive"])]
        ], [
            'sendCountry.required' => "Sender country is required",
            'getCountry.required' => "Receiver country  is required",
            'serviceId.required' => "Service  is required"
        ]);
        if ($validate->fails()) {
            return response()->json(['errors' => $validate->errors()]);
        }

        $country = Country::select('id', 'name', 'slug', 'code', 'minimum_amount', 'rate', 'facilities', 'image')->whereIn('id', [$request->sendCountry, $request->getCountry])->where('status', 1)->get()->map(function ($item) {
            $item->minimum_amount = (float)$item->minimum_amount;
            $item->rate = (float)$item->rate;
            return $item;
        });

        if ($request->has('sendCountry')) {
            $data['sendCountry'] = $country->where('id', $request->sendCountry)->first();
            if (!$data['sendCountry']) {
                return response()->json(['errors' => ['sender' => ['Sender Country Not Found']]]);
            }
        }
        if ($request->has('getCountry')) {
            $data['receiveCountry'] = $country->where('id', $request->getCountry)->first();
            if (!$data['receiveCountry']) {
                return response()->json(['errors' => ['receiver' => ['Receiver Country Not Found']]]);
            }
            if (!$data['receiveCountry']->facilities) {
                return response()->json(['errors' => ['receiver_service' => ['Receiver Country Service Not Available']]]);
            }
            $data['receiveCountryFacilities'] = collect($data['receiveCountry']->facilities)->where('id', $request->serviceId)->first();
            if (!$data['receiveCountryFacilities']) {
                return response()->json(['errors' => ['receiver_service' => ['Receiver Country Service Not Available']]]);
            }
        }

        $amount = $request->amount;
        $rate = $data['receiveCountry']['rate'] / $data['sendCountry']['rate'];

        $data['rate'] = round($rate, basicControl()->fraction_number);

        $data['send_currency'] = $data['sendCountry']['code'];
        $data['receive_currency'] = $data['receiveCountry']['code'];

        if ($request->sendReceive == "send") {
            $data['send_amount'] = $amount;
            $data['fees'] = round(getCharge($amount, $data['receiveCountry']->id, $data['receiveCountryFacilities']->id), 2);
            $data['total_payable'] = round($amount + $data['fees'], basicControl()->fraction_number);
            $data['recipient_get'] = round($amount * $rate, 2);
        }

        if ($request->sendReceive == "receive") {
            $data['send_amount'] = round($amount / $rate, 2);
            $data['fees'] = round(getCharge($amount, $data['receiveCountry']->id, $data['receiveCountryFacilities']->id) / $rate, 2);
            $data['total_payable'] = round(($amount / $rate) + $data['fees'], basicControl()->fraction_number);
            $data['recipient_get'] = round($amount, 2);
        }

        $result['status'] = true;
        $result['message'] = null;
        $result['data'] = $data;
        return response($result, 200);
    }


    public function calculationProceed(Request $request)
    {
//        if (config('basic.daily_limit') < $this->perPersonTransaction()['daily']) {
//            $result['status'] = false;
//            $result['message'] = 'You have reached the daily limit ' . config('basic.daily_limit') . ' ' . config('basic.currency');
//            $result['data'] = [];
//            return response($result, 200);
//        }
//
//        if (config('basic.monthly_limit') < $this->perPersonTransaction()['monthly']) {
//            $result['status'] = false;
//            $result['message'] = 'You have reached the monthly limit ' . config('basic.monthly_limit') . ' ' . config('basic.currency');
//            $result['data'] = [];
//            return response($result, 200);
//        }

        $validator = validator()->make($request->all(), [
            'amount' => 'required|numeric',
            'sendCountry' => 'required|numeric',
            'getCountry' => 'required|numeric',
            'country_service' => 'required|numeric', // serviceId
            'payout_network' => 'required', //
            'sendReceive' => ['required', Rule::in(["send", "receive"])]
        ], [
            'sendCountry.required' => "Sender country is required",
            'getCountry.required' => "Please select a currency to receive",
            'country_service.required' => "Service is required",
            'payout_network.required' => "Provider must be required",
            'amount.required' => "Amount is required",
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()]);
        }
        $country = Country::select('id', 'name', 'slug', 'code', 'minimum_amount', 'rate', 'facilities', 'image')->whereIn('id', [$request->sendCountry, $request->getCountry])->where('status', 1)->get();
        if ($request->has('sendCountry')) {
            $sendCountry = $country->where('id', $request->sendCountry)->first();
            if (!$sendCountry) {
                $result['status'] = false;
                $result['message'] = 'Sender Country Not Found';
                $result['data'] = [];
                return response($result, 200);
            }

            if ($request->amount < $sendCountry->minimum_amount) {
                $result['status'] = false;
                $result['message'] = 'Minimum amount ' . getAmount($sendCountry->minimum_amount, basicControl()->fraction_number) . " " . $sendCountry->code;
                $result['data'] = [];
                return response($result, 200);
            }
        }

        if ($request->has('getCountry')) {
            $receiveCountry = $country->where('id', $request->getCountry)->first();
            if (!$receiveCountry) {
                $result['status'] = false;
                $result['message'] = 'Receiver Country Not Found';
                $result['data'] = [];
                return response($result, 200);
            }
            if (!$receiveCountry->facilities) {
                $result['status'] = false;
                $result['message'] = 'Receiver Country Service Not Available';
                $result['data'] = [];
                return response($result, 200);
            }
            $receiveCountryFacilities = collect($receiveCountry->facilities)->where('id', $request->country_service)->first();
            if (!$receiveCountryFacilities) {
                $result['status'] = false;
                $result['message'] = 'Receiver Country Service Not Available';
                $result['data'] = [];
                return response($result, 200);
            }
            $provider = CountryService::tobase()->where([
                'id' => $request->payout_network,
                //'country_id' => $receiveCountry->id,
                //'service_id' => $receiveCountryFacilities->id,
                //'status' => 1
            ])->first();
            if (!$provider) {
                $result['status'] = false;
                $result['message'] = 'Invalid Provider';
                $result['data'] = [];
                return response($result, 200);
            }
        }

        $amount = $request->amount;
        $rate = $receiveCountry['rate'] / $sendCountry['rate'];

        $data['rate'] = round($rate, basicControl()->fraction_number);

        $data['send_currency'] = $sendCountry['code'];
        $data['receive_currency'] = $receiveCountry['code'];

        if ($request->sendReceive == "send") {
            $data['send_amount'] = $amount;
            $data['fees'] = round(getCharge($amount, $receiveCountry->id, $receiveCountryFacilities->id), 2);
            $data['total_payable'] = round($amount + $data['fees'], basicControl()->fraction_number);
            $data['recipient_get'] = round($amount * $rate, 2);
        }

        if ($request->sendReceive == "receive") {
            $data['send_amount'] = round($amount / $rate, 2);
            $data['fees'] = round(getCharge($amount, $receiveCountry->id, $receiveCountryFacilities->id) / $rate, 2);
            $data['total_payable'] = round(($amount / $rate) + $data['fees'], basicControl()->fraction_number);
            $data['recipient_get'] = round($amount, 2);
        }

        $invoice = invoice();

        $sendMoney = new  SendMoney();
        $sendMoney->invoice = $invoice;
        $sendMoney->user_id = auth()->user()->id;
        $sendMoney->send_currency_id = $sendCountry['id'];
        $sendMoney->receive_currency_id = $receiveCountry['id'];
        $sendMoney->service_id = $receiveCountryFacilities->id;
        $sendMoney->country_service_id = $provider->id;
        $sendMoney->send_curr_rate = $sendCountry['rate'];
        $sendMoney->send_curr = $sendCountry['code'];
        $sendMoney->receive_curr = $receiveCountry['code'];
        $sendMoney->rate = $rate;
        $sendMoney->send_amount = $data['send_amount'];
        $sendMoney->fees = $data['fees'];
        $sendMoney->payable_amount = $data['total_payable'];
        $sendMoney->recipient_get_amount = $data['recipient_get'];
        $sendMoney->save();


        $sendMoney->send_curr_rate = (float)$sendMoney->send_curr_rate;
        $sendMoney->send_amount = (float)$sendMoney->send_amount;


        $result['status'] = true;
        $result['message'] = 'Calculation Initiated';
        $result['data'] = ['sendMoney' => $sendMoney, 'redirect' => true, 'url' => route('transfer-form', $sendMoney->invoice)];
        return response($result, 200);
    }

    public function transferForm($invoice)
    {
        $sendMoney = SendMoney::where('invoice', $invoice)->with('provider')->orderBy('id', 'desc')->first();
        if (!$sendMoney) {
            $result['status'] = false;
            $result['message'] = 'Invalid Request';
            $result['data'] = [];
            return response($result, 200);
        }

        $user = auth()->user();
        if ($sendMoney->user_id != $user->id) {
            $result['status'] = false;
            $result['message'] = 'Unauthentic Transaction';
            $result['data'] = [];
            return response($result, 200);

        }
        /*
        if ($sendMoney->status != '0') {
            $result['status'] = false;
            $result['message'] =  'You are not eligible to change request.';
            $result['data'] = [];
            return response($result, 200);
        }
        */

        $myCollection = collect(config('country'))->map(function ($row) {
            return collect($row);
        });
        $data['dial_codes'] = $myCollection->sortBy('code');
        $data['form_dynamic_fields'] = optional($sendMoney->provider)->services_form;
        $data['receipt'] = [
            'service' => optional($sendMoney->service)->name,
            'exchange' => trans($sendMoney->send_curr) . ' - ' . trans($sendMoney->receive_curr),
            'Send_Amount' => getAmount($sendMoney->send_amount) . ' ' . trans($sendMoney->send_curr),
            'Fees' => getAmount($sendMoney->fees) . ' ' . trans($sendMoney->send_curr),
            'Exchange_rate' => [
                'from' => trans('1') . ' ' . trans($sendMoney->send_curr),
                'to' => getAmount($sendMoney->rate, config('basic.fraction_number')) . ' ' . trans($sendMoney->receive_curr)
            ],
            'You_pay_in_total' => getAmount($sendMoney->send_amount, config('basic.fraction_number')) . ' ' . trans($sendMoney->send_curr),
            'Your_recipient_gets' => getAmount($sendMoney->recipient_get_amount, config('basic.fraction_number')) . ' ' . trans($sendMoney->receive_curr),
        ];
        $data['sourceFunds'] = SourceFund::tobase()->select('title')->get();
        $data['sendingPurpose'] = SendingPurpose::tobase()->select('title')->get();
        $data['sendMoney'] = $sendMoney;
        $data['flutterwave'] = array_keys(config('flutterwave'));


        $result['status'] = true;
        $result['message'] = null;
        $result['data'] = $data;
        return response($result, 200);

    }

    public function transferFormSubmit($invoice, Request $request)
    {
        $sendMoney = SendMoney::where('invoice', $invoice)->orderBy('id', 'desc')->with('provider')->first();
        if (!$sendMoney) {
            $result['status'] = false;
            $result['message'] = 'Invalid Request';
            $result['data'] = [];
            return response($result, 200);
        }
        $user = auth()->user();
        if ($sendMoney->user_id != $user->id) {
            $result['status'] = false;
            $result['message'] = 'Unauthentic Transaction';
            $result['data'] = [];
            return response($result, 200);

        }
        /*
        if ($sendMoney->status != '0') {
            $result['status'] = false;
            $result['message'] =  'You are not eligible to change request.';
            $result['data'] = [];
            return response($result, 200);
        }
        */

        $rules = [];
        $rules['recipient_name'] = ['sometimes', 'required', 'max:91'];
        $rules['recipient_contact_no'] = ['required', 'max:20'];
        $rules['recipient_email'] = ['required', 'email', 'max:30'];
        $rules['fund_source'] = ['sometimes', 'required', 'max:255'];
        $rules['purpose'] = ['sometimes', 'required', 'max:255'];
        $rules['promo_code'] = ['nullable', 'numeric'];
        $inputField = [];
        if (optional($sendMoney->provider)->services_form) {
            foreach ($sendMoney->provider->services_form as $key => $cus) {
                $rules[$key] = [$cus->validation];
                if ($cus->type == 'file') {
                    array_push($rules[$key], trim($cus->validation));
                    array_push($rules[$key], 'max:' . trim($cus->field_length));
                }
                if ($cus->type == 'text') {
                    array_push($rules[$key], trim($cus->validation));
                    if ($cus->length_type == 'max') {
                        array_push($rules[$key], 'max:' . trim($cus->field_length));
                    } elseif ($cus->length_type == 'digits') {
                        array_push($rules[$key], 'digits:' . trim($cus->field_length));
                    }
                }
                if ($cus->type == 'textarea') {
                    array_push($rules[$key], trim($cus->validation));
                    array_push($rules[$key], 'max:' . trim($cus->field_length));
                }
                $inputField[] = $key;
            }
        }

        $validator = validator()->make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()]);
        }


        $req = Purify::clean($request->all());
        $req = (object)$req;
        $path = config('location.send_money.path') . date('Y') . '/' . date('m') . '/' . date('d');
        $collection = collect($req);

        $reqField = [];
        if (optional($sendMoney->provider)->services_form) {
            foreach ($collection as $k => $v) {
                foreach (optional($sendMoney->provider)->services_form as $inKey => $inVal) {
                    if ($k != $inKey) {
                        continue;
                    } else {

                        if ($inVal->type == 'file') {
                            if ($request->has($inKey)) {
                                try {
                                    $img = $_POST[$inKey];
                                    $image_parts = explode(";base64,", $img);
                                    $image_type_aux = explode("image/", $image_parts[0]);
                                    $image_type = $image_type_aux[1];
                                    $image_base64 = base64_decode($image_parts[1]);

                                    $filename = uniqid() . '.jpg';
                                    $file = $path . $filename;
                                    @file_put_contents($file, $image_base64);
                                    $reqField[$inKey] = [
                                        'field_name' => $filename,
                                        'file_location' => $path,
                                        'type' => $inVal->type,
                                    ];
                                } catch (\Exception $exp) {
                                    $result['status'] = false;
                                    $result['message'] = 'Could not upload your ' . $inKey;
                                    $result['data'] = [];
                                    return response($result, 200);
                                }
                            }
                        } else {
                            $reqField[$inKey] = [
                                'field_name' => $v,
                                'type' => $inVal->type,
                            ];
                        }
                    }
                }
            }
            $sendMoney['user_information'] = $reqField;
        } else {
            $sendMoney['user_information'] = null;
        }
        $sendMoney->recipient_name = @$req->recipient_name;
        $sendMoney->recipient_contact_no = $req->recipient_contact_no;
        $sendMoney->recipient_email = $req->recipient_email;
        $sendMoney->fund_source = @$req->fund_source;
        $sendMoney->purpose = @$req->purpose;


        if ($request->promo_code != null) {
            $coupon = Coupon::where('code', trim($request->promo_code))->whereNull('user_id')->first();
            if (!$coupon) {
                $result['status'] = false;
                $result['message'] = 'Invalid promo code';
                $result['data'] = [];
                return response($result, 200);
            }
            if ($sendMoney->promo_code == null) {
                $sendMoney->discount = ($sendMoney->payable_amount * $coupon->reduce_fee) / 100;
                $sendMoney->promo_code = $coupon->code;
                $coupon->user_id = $user->id;
                $coupon->used_at = Carbon::now();
                $coupon->update();
            }
        }

        //$sendMoney->uuid = Str::uuid(); //Draft
        $sendMoney->status = 2; //Draft
        $sendMoney->save();

        $result['status'] = true;
        $result['message'] = 'Information Saved Successfully';
        return response($result, 200);
    }

    protected function perPersonTransaction()
    {
        $user = auth()->user();
        $query = SendMoney::query();
        $result['daily'] = (clone $query)->toBase()
            ->where('user_id', $user->id)
            ->whereIn('payment_status', [1, 2])
            ->whereIn('status', [1, 3])
            ->select(DB::raw('*'))
            ->whereRaw('Date(created_at) = CURRENT_DATE()')->get()->map(function ($item) {
                $totalPay = $item->payable_amount - $item->discount;
                return ($totalPay / $item->send_curr_rate) * config('basic.rate');
            })->sum();

        $result['monthly'] = (clone $query)->toBase()
            ->where('user_id', $user->id)
            ->whereIn('payment_status', [1, 2])
            ->whereIn('status', [1, 3])
            ->select(DB::raw('*'))
            ->whereRaw('MONTH(created_at) = MONTH(CURRENT_DATE())')->get()->map(function ($item) {
                $totalPay = $item->payable_amount - $item->discount;
                return ($totalPay / $item->send_curr_rate) * config('basic.rate');
            })->sum();
        return $result;
    }

    public function pusherConfig()
    {
        try {
            $data['apiKey'] = env('PUSHER_APP_KEY');
            $data['cluster'] = env('PUSHER_APP_CLUSTER');
            $data['channel'] = 'user-notification.' . Auth::id();
            $data['event'] = 'UserNotification';
            $result['status'] = true;
            $result['data'] = $data;
            return response($result, 200);
        } catch (\Exception $e) {
            $result['status'] = false;
            $result['message'] = 'Invalid Error';
            return response($result, 200);
        }
    }

    public function appConfig()
    {
        try {
            $basic = basicControl();
            $data['baseColor'] = $basic->app_color;
            $data['version'] = $basic->app_version;
            $data['appBuild'] = $basic->app_build;
            $data['isMajor'] = $basic->is_major;
            $data['flutterwave_payout_service'] = $basic->flutterwave_payout_service;
            $data['paymentSuccessUrl'] = route('success');
            $data['paymentFailedUrl'] = route('failed');
            $result['status'] = true;
            $result['data'] = $data;
            return response($result, 200);
        } catch (\Exception $exception) {
            $result['status'] = false;
            $result['message'] = 'Invalid Error';
            return response($result, 200);
        }
    }

    public function language($id = null)
    {
        try {
            if (!$id) {
                $data['languages'] = Language::select(['id', 'name', 'short_name'])->where('is_active', 1)->get();
                $result['status'] = true;
                $result['data'] = $data;
                return response($result, 200);
            }
            $lang = Language::where('is_active', 1)->find($id);
            if (!$lang) {
                $result['status'] = false;
                $result['message'] = 'Record not found';
                return response($result, 200);
            }

            $json = file_get_contents(resource_path('lang/') . $lang->short_name . '.json');
            if (empty($json)) {
                $result['status'] = false;
                $result['message'] = 'Record not found';
                return response($result, 200);
            }

            $json = json_decode($json, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            $result['status'] = true;
            $result['data'] = $json;
            return response($result, 200);
        } catch (\Exception $e) {
            $result['status'] = false;
            $result['message'] = $e->getMessage();
            return response($result, 200);
        }
    }

    public function getVerification()
    {
        $user = auth()->user();

        if ($user->identity_verify == 1) {
            $value['identityMessage'] = 'Your KYC submission has been pending';
        } elseif ($user->identity_verify == 2) {
            $value['identityMessage'] = 'Your KYC already verified';
        } else {
            $value['identityMessage'] = null;
        }

        if ($user->address_verify == 1) {
            $value['addressMessage'] = 'Your KYC submission has been pending';
        } elseif ($user->address_verify == 2) {
            $value['addressMessage'] = 'Your KYC already verified';
        } else {
            $value['addressMessage'] = null;
        }

        $value['identityFormList'] = IdentifyForm::where('status', 1)->get();

        $result['status'] = true;
        $result['data'] = $value;
        return response($result, 200);
    }

    public function identitySubmit(Request $request)
    {
        $identityFormList = IdentifyForm::where('status', 1)->get();
        $rules['identity_type'] = ["required", Rule::in($identityFormList->pluck('slug')->toArray())];
        $identity_type = $request->identity_type;
        $identityForm = IdentifyForm::where('slug', trim($identity_type))->where('status', 1)->first();

        $params = $identityForm->services_form;

        $rules = [];
        $inputField = [];
        $verifyImages = [];

        if ($params != null) {
            foreach ($params as $key => $cus) {
                $rules[$key] = [$cus->validation];
                if ($cus->type == 'file') {
                    array_push($rules[$key], 'image');
                    array_push($rules[$key], 'mimes:jpeg,jpg,png');
                    array_push($rules[$key], 'max:2048');
                    array_push($verifyImages, $key);
                }
                if ($cus->type == 'text') {
                    array_push($rules[$key], 'max:191');
                }
                if ($cus->type == 'textarea') {
                    array_push($rules[$key], 'max:300');
                }
                $inputField[] = $key;
            }
        }

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            $result['status'] = false;
            $result['message'] = $validator->errors();
            return response($result, 200);
        }


        $path = config('location.kyc.path') . date('Y') . '/' . date('m') . '/' . date('d');
        $collection = collect($request);

        $reqField = [];
        if ($params != null) {
            foreach ($collection as $k => $v) {
                foreach ($params as $inKey => $inVal) {
                    if ($k != $inKey) {
                        continue;
                    } else {
                        if ($inVal->type == 'file') {
                            if ($request->hasFile($inKey)) {
                                try {
                                    $reqField[$inKey] = [
                                        'field_name' => $this->uploadImage($request[$inKey], $path),
                                        'type' => $inVal->type,
                                    ];
                                } catch (\Exception $exp) {
                                    $result['status'] = false;
                                    $result['message'] = 'Could not upload your ' . $inKey;
                                    return response($result, 200);
                                }
                            }
                        } else {
                            $reqField[$inKey] = [
                                'field_name' => $v,
                                'type' => $inVal->type,
                            ];
                        }
                    }
                }
            }
        }

        try {

            DB::beginTransaction();

            $user = auth()->user();
            $kyc = new KYC();
            $kyc->user_id = $user->id;
            $kyc->kyc_type = $identityForm->slug;
            $kyc->details = $reqField;
            $kyc->save();

            $user->identity_verify = 1;
            $user->save();

            if (!$kyc) {
                DB::rollBack();

                $result['status'] = false;
                $result['message'] = "Failed to submit request";
                return response($result, 200);
            }
            DB::commit();

            $result['status'] = true;
            $result['message'] = "KYC has been submitted";
            return response($result, 200);

        } catch (\Exception $e) {
            $result['status'] = false;
            $result['message'] = $e->getMessage();
            return response($result, 200);
        }
    }

    public function addressSubmit(Request $request)
    {
        $rules = [];
        $rules['addressProof'] = ['image', 'mimes:jpeg,jpg,png', 'max:2048'];
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            $result['status'] = false;
            $result['message'] = $validator->errors();
            return response($result, 200);
        }

        $path = config('location.kyc.path') . date('Y') . '/' . date('m') . '/' . date('d');

        $reqField = [];
        try {
            if ($request->hasFile('addressProof')) {
                $reqField['addressProof'] = [
                    'field_name' => $this->uploadImage($request['addressProof'], $path),
                    'type' => 'file',
                ];
            } else {
                $result['status'] = false;
                $result['message'] = 'Please select a ' . 'address Proof';
                return response($result, 200);
            }
        } catch (\Exception $exp) {
            $result['status'] = false;
            $result['message'] = 'Could not upload your ' . 'address Proof';
            return response($result, 200);
        }

        try {

            DB::beginTransaction();
            $user = auth()->user();
            $kyc = new KYC();
            $kyc->user_id = $user->id;
            $kyc->kyc_type = 'address-verification';
            $kyc->details = $reqField;
            $kyc->save();
            $user->address_verify = 1;
            $user->save();

            if (!$kyc) {
                DB::rollBack();

                $result['status'] = false;
                $result['message'] = 'Failed to submit request';
                return response($result, 200);
            }
            DB::commit();

            $result['status'] = true;
            $result['message'] = 'Your request has been submitted.';
            return response($result, 200);

        } catch (\Exception $e) {
            $result['status'] = false;
            $result['message'] = $e->getMessage();
            return response($result, 200);
        }
    }

    public function flutterwaveData(Request $request)
    {
        return config('flutterwave');
    }

    public function verifyAccount(Request $request)
    {
        $validator = validator()->make($request->all(), [
            'bank_code' => 'required',
            'account_number' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()]);
        }
        $postParam['account_bank'] = $request->bank_code;
        $postParam['account_number'] = $request->account_number;
        $response = Flutterwave::AccountVerification($postParam);
        $response = json_decode($response);
        return $response;
    }

}
