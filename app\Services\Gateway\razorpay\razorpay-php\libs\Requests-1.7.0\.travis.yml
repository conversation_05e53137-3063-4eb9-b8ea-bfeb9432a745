language: php
matrix:
  fast_finish: true
  include:
    - php: 5.2
    - php: 5.3
    - php: 5.4
    - php: 5.5
    - php: 5.6
      env: TEST_COVERAGE=1
    - php: 7.0
    - php: hhvm

# Use new container infrastructure
sudo: false

cache:
  directories:
    - $HOME/.cache/pip
    - $HOME/.composer/cache
    - vendor

install:
 # Setup the test server
 - phpenv local 5.5
 - composer install --dev --no-interaction
 - TESTPHPBIN=$(phpenv which php)
 - phpenv local --unset

 # Setup the proxy
 - pip install --user mitmproxy~=0.15

before_script:
 - PHPBIN=$TESTPHPBIN PORT=8080 vendor/bin/start.sh
 - export REQUESTS_TEST_HOST_HTTP="localhost:8080"

 # Work out of the tests directory
 - cd tests
 - PROXYBIN="$HOME/.local/bin/mitmdump" PORT=9002 utils/proxy/start.sh
 - PROXYBIN="$HOME/.local/bin/mitmdump" PORT=9003 AUTH="test:pass" utils/proxy/start.sh
 - export REQUESTS_HTTP_PROXY="localhost:9002"
 - export REQUESTS_HTTP_PROXY_AUTH="localhost:9003"
 - export REQUESTS_HTTP_PROXY_AUTH_USER="test"
 - export REQUESTS_HTTP_PROXY_AUTH_PASS="pass"

 # Ensure the HTTPS test instance on Heroku is spun up
 - curl -s -I http://requests-php-tests.herokuapp.com/ > /dev/null

script:
 - phpunit --coverage-clover clover.xml

after_script:
 - utils/proxy/stop.sh
 - cd ..
 - phpenv local 5.5
 - PATH=$PATH vendor/bin/stop.sh
 - test $TEST_COVERAGE && bash <(curl -s https://codecov.io/bash)
 - phpenv local --unset
