:root {
    --primary: #1fd3c6;
    --secondary: #37517e;
    --gradient: linear-gradient(to right, #1fd3c6 0%, #488ff9 100%);
}

.logo-text img {
    width: 100%;
}

.table-responsive {
    overflow-x: initial;
}
.breadcrumb {
    text-transform: capitalize;
}
.navbar-search-form {
    position: relative;
}
#navbar_search_result_area {
    position: absolute;
    top: 84%;
    background-color: #fff;
    width: 100%;
    left: 0;
    border-radius: 10px 10px 10px 10px;
    z-index: 99;
    overflow: hidden;
    box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.25);
}

.navbar_search_result {
    max-height: 310px;
    overflow: auto;
    list-style: none;
    padding: 0px 20px 0px;
}
.navbar_search_result::-webkit-scrollbar {
    width: 2px;
}
.navbar_search_result::-webkit-scrollbar-track {
    box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.3);
}
.navbar_search_result::-webkit-scrollbar-thumb {
    background-color: darkgrey;
    outline: 1px solid slategrey;
}
.navbar_search_result li {
    padding: 0px;
    line-height: 2.5;
    border-bottom: 1px solid #e5e5e5;
}
.navbar_search_result li a {
    color: #363636;
    font-size: 13px;
}
.navbar_search_resut li a:hover {
    color: blue;
}

.width-40p {
    width: 40px;
}

.image-input {
    position: relative;
    width: 100%;
    min-height: 300px;
    background: #f0f8ff;
}

.image-input #admin_image,
.image-input #image,
.image-input #favicon,
.image-input #meta_image {
    position: absolute;
    opacity: 0;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    cursor: pointer;
}

.image-input #admin_image-label,
.image-input #image-label,
.image-input #favicon-label,
.image-input #meta_image-label {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    padding: 70px 100px;
    z-index: 5;
    opacity: 0.3;
    cursor: pointer;
    background-color: #fff;
    font-size: 25px;
    border: 2px dashed #000;
    margin: auto;
    text-align: center;
}

.image-input .preview-image {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    max-width: 150px;
}

.table thead {
    border-spacing: 0;
}
.table td {
    position: relative;
}

.table .table-minimal-data-span {
    position: absolute;
    right: 1px;
    background: #65ab84;
    padding: 0px 5px;
    bottom: 1px;
}
/*.table-header-right .show-hide-icon i{*/
/*    transition: all 10s ease-out;*/
/*}*/
.show-hide-icon:focus.show-hide-icon.collapsed i:before {
    content: "\f077";
    color: red;
    transition: all 2s ease-out;
}

.table-header-right .show-hide-icon i:before {
    transform: rotate(180deg);
    transition: all 2s ease-out;
}
.categories-show-table {
    border-collapse: separate;
    border-spacing: 0;
}
.check-box-width-50 {
    min-width: 50px;
}

.btn-swap {
    background-color: #fff;
    border: 2px solid #22ca80;
    transition: all 0.3s;
    position: relative;
    cursor: pointer;
    color: #22ca80;
    font-weight: 400;
}

.btn-swap span {
    position: relative;
    z-index: 1;
}

.btn-swap:hover {
    color: #fff;
    background: #22ca80;
    transform: scale(1.1);
}

.btn-swap:hover:after {
    width: 100%;
    z-index: 0;
}

.btn-swap:after {
    content: "";
    position: absolute;
    transition: all 0.3s;
    width: 0%;
    height: 100%;
    top: 0;
    left: 0;
    background: #22ca80;
}
.btn-swap.red {
    background-color: #fff;
    border: 2px solid #ff4f70;
    transition: all 0.3s;
    position: relative;
    cursor: pointer;
    color: #ff4f70;
    font-weight: 400;
}
.btn-swap.red:after {
    content: "";
    position: absolute;
    transition: all 0.3s;
    width: 0%;
    height: 100%;
    top: 0;
    left: 0;
    background: #ff4f70;
}
.btn-swap.red span {
    position: relative;
    z-index: 1;
}

.btn-swap.red:hover {
    color: #fff;
    transform: scale(1.1);
}

.btn-swap.red:hover:after {
    width: 100%;
    z-index: 0;
}

/*Checkboxes styles*/
input[type="checkbox"].tic-check {
    display: none;
}

input[type="checkbox"].tic-check + label {
    display: block;
    position: relative;
    color: transparent;
    padding: 12px 0;
}

input[type="checkbox"].tic-check + label:last-child {
    margin-bottom: 0;
    margin-right: 10px;
}

input[type="checkbox"].tic-check + label:before {
    content: "";
    display: block;
    width: 15px;
    height: 15px;
    border: 2px solid #22ca80;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0.6;
    -webkit-transition: all 0.12s, border-color 0.08s;
    transition: all 0.12s, border-color 0.08s;
}

input[type="checkbox"].tic-check:checked + label:before {
    width: 10px;
    top: -5px;
    left: 5px;
    border-radius: 0;
    opacity: 1;
    border-top-color: transparent;
    border-left-color: transparent;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}

.search-wrapper .dropdown.dropdown-lg .dropdown-menu {
    margin-top: 0;
    padding: 5px 20px;
}
.search-wrapper .input-group-btn .btn-group {
    display: flex !important;
}
.search-wrapper .btn-group .btn {
    border-radius: 0;
    margin-left: 0;
}

.search-wrapper .form-horizontal .form-group {
    margin-left: 0;
    margin-right: 0;
}
.table-group-title {
    position: relative;
    padding-left: 40px;
    transition: all 0.5s;
}
.table-group-title:before {
    background: #22ca80 none repeat scroll 0 0;
    top: 50%;
    transform: translate(0, -50%);
    content: "";
    height: 6px;
    border-radius: 20px;
    left: 0;
    position: absolute;
    width: 30px;
    transition: all 0.5s;
}
.table-group-title:hover {
    padding-left: 50px;
}
.table-group-title:hover:before {
    width: 40px;
}

.divider {
    position: relative;
    height: 25px;
}
.switch-field input {
    display: none;
}

.switch-field label {
    display: inline-block;
    background-color: #fff;
    color: #333;
    font-size: 1em;
    font-weight: normal;
    text-align: center;
    text-shadow: none;
    padding: 0.5em 2em;
    min-width: 100px;
    border: 0.05em solid rgba(0, 0, 0, 0.15);
    -webkit-transition: all 0.1s ease-in-out;
    -moz-transition: all 0.1s ease-in-out;
    -ms-transition: all 0.1s ease-in-out;
    -o-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out;
}

.switch-field label:hover {
    cursor: pointer;
}

.switch-field input:checked + label {
    background-color: #22ca80;
    color: white;
}

.switch-field label:first-of-type {
    border-radius: 3px 0 0 3px;
}

.switch-field label:last-of-type {
    border-radius: 0 3px 3px 0;
    border-radius: 20px;
}

/*User Section Design*/
/*-------------------------------------*/
.header-user .navbar-brand {
    background: #f6f9fb;
}
#navbar-user.show {
    display: block !important;
}

.table td {
    max-width: 500px;
}

#accordion .card-header {
    margin-bottom: 8px;
}
#accordion .accordion-title {
    position: relative;
    display: block;
    padding: 8px 0 8px 50px;
    background: #213744;
    border-radius: 8px;
    overflow: hidden;
    text-decoration: none;
    color: #fff;
    font-size: 16px;
    font-weight: 700;
    width: 100%;
    text-align: left;
    transition: all 0.4s ease-in-out;
}
#accordion .accordion-title i {
    position: absolute;
    width: 40px;
    height: 100%;
    left: 0;
    top: 0;
    color: #fff;
    background: radial-gradient(rgba(33, 55, 68, 0.8), #213744);
    text-align: center;
    border-right: 1px solid transparent;
}
#accordion .accordion-title:hover {
    padding-left: 60px;
    background: #213744;
    color: #fff;
}
#accordion .accordion-title:hover i {
    border-right: 1px solid #fff;
}
#accordion .accordion-body {
    padding: 40px 55px;
}
#accordion .accordion-body ul {
    list-style: none;
    margin-left: 0;
    padding-left: 0;
}
#accordion .accordion-body li {
    padding-left: 1.2rem;
    text-indent: -1.2rem;
}
#accordion .accordion-body li:before {
    content: "\f10a";
    padding-right: 5px;
    font-family: "Flaticon";
    font-size: 16px;
    font-style: normal;
    color: #213744;
}

@media screen and (min-width: 768px) {
    .search-wrapper .dropdown.dropdown-lg {
        position: inherit !important;
    }
    .search-wrapper .dropdown.dropdown-lg .dropdown-menu {
        min-width: 500px;
    }
}
@media screen and (max-width: 768px) {
    .card-form .card-body {
        margin: 20px 20px;
    }
    .card-form {
        margin: 0;
    }
}

/* Custom end */

.custom-switch-btn {
    position: relative;
    width: 100%;
}

.custom-switch-checkbox {
    display: none;
}

.custom-switch-checkbox-label {
    display: block;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    border: 2px solid #5f76e8;
    border-radius: 20px;
}

.custom-switch-checkbox-inner {
    display: contents;
    /* width: 200%;
    margin-left: -100%; */
    transition: margin 0.3s ease-in 0s;
}
.custom-switch-checkbox-inner2 {
    display: contents;
    /* width: 200%;
    margin-left: -100%; */
    transition: margin 0.3s ease-in 0s;
}

.custom-switch-checkbox-inner:before,
.custom-switch-checkbox-inner:after {
    display: block;
    float: left;
    width: 50%;
    height: 35px;
    padding: 0;
    line-height: 22px;
    font-size: 12px;
    color: black;

    box-sizing: border-box;
}

.custom-switch-checkbox-inner2:before,
.custom-switch-checkbox-inner2:after {
    display: block;
    float: left;
    width: 50%;
    height: 35px;
    padding: 0;
    line-height: 22px;
    font-size: 12px;
    color: black;

    box-sizing: border-box;
}

.custom-switch-checkbox-inner:before {
    content: "ON";
    padding-left: 6px;
    font-weight: 500;
    /* background-color: #3ac5c9; */
    color: #000;
    display: flex;
    justify-content: center; /* align horizontal */
    align-items: center; /* align vertical */
}

.custom-switch-checkbox-inner:after {
    content: "OFF";
    padding-right: 6px;
    font-weight: 500;
    /* background-color: #e85764; */
    color: #000;
    text-align: right;
    display: flex;
    justify-content: center; /* align horizontal */
    align-items: center; /* align vertical */
}

.custom-switch-checkbox-inner2:before {
    content: "Active";
    padding-left: 6px;
    font-weight: 500;
    /* background-color: #3ac5c9; */
    color: #000;
    display: flex;
    justify-content: center; /* align horizontal */
    align-items: center; /* align vertical */
}

.custom-switch-checkbox-inner2:after {
    content: "Deactive";
    padding-right: 6px;
    font-weight: 500;
    /* background-color: #e85764; */
    color: #000;
    text-align: right;
    display: flex;
    justify-content: center; /* align horizontal */
    align-items: center; /* align vertical */
}

.custom-switch-checkbox-switch {
    display: block;
    width: 47%;
    margin: 5px;
    background: #5f76e8;
    position: absolute;
    top: 0;
    bottom: 0;
    right: auto;
    left: 0;
    border: 2px solid #5f76e8;
    border-radius: 20px;
    transition: all 0.3s ease-in 0s;
    opacity: 0.8;
}

.custom-switch-checkbox:checked
    + .custom-switch-checkbox-label
    .custom-switch-checkbox-inner {
    margin-left: 0;
}

.custom-switch-checkbox:checked
    + .custom-switch-checkbox-label
    .custom-switch-checkbox-inner2 {
    margin-left: 0;
}

.custom-switch-checkbox:checked
    + .custom-switch-checkbox-label
    .custom-switch-checkbox-switch {
    right: 0px;
    left: auto;
}

.cursor-pointer {
    cursor: pointer;
}

.select2-search--dropdown {
    display: block;
    padding: 10px 4px;
}
span.select2-dropdown.select2-dropdown--below {
    box-shadow: 0px 20px 33px -7px;
    margin-top: 12px;
}
.select2-container--default .select2-selection--single {
    background-color: #fff;
    border: 1px solid transparent;
    border-radius: 4px;
}
.select2-container--default
    .select2-selection--single
    .select2-selection__rendered {
    color: #444;
    display: block;
    width: 100%;
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #e9ecef;
    border-radius: 2px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.scroll-height {
    height: calc(100vh - 111px);
}

.sideNavTicket {
    background: #dfe7f3;
    color: #dbe5d8;
    /*color: #307fb0;*/
    /*color: #edf1eb;;*/
}

.chat-list .chat-item .chat-content .msg {
    background-color: #eef5ff;
    font-size: 14px;
    max-width: 95%;
}

li.chat-item.list-style-none.replied.mt-3.text-right {
    display: flex;
    flex-direction: row-reverse;
}

.chat-list .chat-item.replied .chat-img {
    margin-left: 15px;
}

.chat-list .chat-item.replied .chat-content .msg {
    background-color: #e4fbf8;
    text-align: left;
}

.button-wrapper {
    position: relative;
    background: rebeccapurple;
    top: -5px;
}

.button-wrapper span.label {
    position: relative;
    z-index: 0;
    background: #00bfff;
    cursor: pointer;
    color: #fff;
    font-size: 18px;
}

#upload {
    opacity: 0;
    cursor: pointer;
}
.new-file-upload {
    position: relative;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: initial;
    overflow: hidden;
    width: 42px;
    height: 42px;
    border-radius: 50%;
    background-color: #5f76e8;
    cursor: pointer;
}
.new-file-upload input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 42px;
    height: 42px;
    border-radius: 50%;
    cursor: pointer;
}
.new-file-upload span,
.new-file-upload span::before {
    cursor: pointer;
}
.upload-btn {
    position: relative;
}
.new-file-upload a {
    color: #fff;
}

.select-files-count {
    position: absolute;
    font-size: 12px;
    white-space: nowrap;
    right: 0px;
}
.ticket-box {
    background: #f9fbfd;
}
.deposit-footer {
    padding: 0.75rem 10px;
}

.sidebar-nav .has-arrow::after {
    position: absolute;
    content: "";
    top: 20px;
}

.sidebar-nav #sidebarnav .sidebar-item .sidebar-link {
    padding: 15px 30px;
}

.ui-sortable-handle {
    cursor: all-scroll;
}
.sidebar-nav #sidebarnav .nav-small-cap {
    margin-top: 10px;
}

table tr {
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    padding: 0.35em;
}

table th {
    font-size: 0.85em;
    letter-spacing: 0.1em;
}
.table td,
.table th {
    padding: 0.625em;
    vertical-align: top;
    border-top: none;
}
.right-dropdown {
    right: -12px !important;
}

.table .thead-primary th,
.table .thead-dark th {
    color: #fff;
    background-color: #5f76e8;
    border-color: #5f76e8;
}

@media screen and (max-width: 991px) {
    table {
        border: 0;
    }

    table thead {
        border: none;
        clip: rect(0 0 0 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }

    table tr {
        /*border-bottom: none;*/
        display: block;
        margin-bottom: 0.625em;

        padding: 0;
    }

    table td {
        border-bottom: none;
        display: block;
        font-size: 0.8em;
        text-align: right;
    }

    table td::before {
        content: attr(data-label);
        float: left;
        font-weight: bold;
    }

    table td:last-child {
        border-bottom: 0;
    }
    .table td {
        max-width: 100%;
    }
}

.invalid-text {
    color: #e74c3c;
}
#Notiflix-Icon-Success,
#Notiflix-Icon-Failure,
#Notiflix-Icon-Warning {
    fill: #fff !important;
}

[v-cloak] {
    display: none;
}

.list-style-none .scrollable {
    max-height: 280px;
    overflow: auto;
    white-space: nowrap;
}
/* width */
.list-style-none .scrollable::-webkit-scrollbar {
    width: 3px;
}

/* Track */
.list-style-none .scrollable::-webkit-scrollbar-track {
    background: #f1f1f1;
}

/* Handle */
.list-style-none .scrollable::-webkit-scrollbar-thumb {
    background: #3250e2;
}

/* Handle on hover */
.list-style-none .scrollable::-webkit-scrollbar-thumb:hover {
    background: #555;
}
.copytext {
    cursor: pointer;
}

.page-breadcrumb {
    text-transform: capitalize;
}
.page-title {
    text-transform: capitalize;
}

#feature .nav-link {
    position: relative;
    background: transparent;
    margin-bottom: 20px;
    border: 1px solid #e5e5e5;
    color: #000;
    transition: all 0.2s ease;
    display: block;
}
#feature .nav-link:hover,
#feature .nav-link.active {
    background: #5f76e8;
    color: #fff;
}
#feature .nav-link::after {
    content: "";
    height: 20px;
    width: 2px;
    background: #e5e5e5;
    position: absolute;
    top: 100%;
    left: calc(50% - 1px);
}
#feature .nav-link:last-child::after {
    display: none;
}
#feature .tab-content {
    flex: 1;
    display: block;
    border: 1px solid #e5e5e5;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    height: 100%;
    margin-left: 20px;
    padding: 15px;
}

#feature .card-body .nav {
    flex-direction: column;
    text-align: center;
}
@media screen and (max-width: 575px) {
    #feature .card-body > div {
        flex-direction: column;
    }
    #feature .card-body .nav {
        flex-direction: row;
        flex-wrap: wrap;
    }
    #feature .card-body .nav .nav-link {
        flex: 1;
        min-width: 90px;
        margin: 5px;
    }
    #feature .nav-link::after {
        display: none;
    }
    #feature .tab-content {
        width: 100%;
        margin-left: 0;
        margin-top: 5px;
    }
}

/*Escrow Details*/

.post-details .post-inner .post-header h6 {
    font-size: 22px !important;
}

.post-details .post-header .title {
    margin-bottom: 36px;
}

@media (min-width: 576px) {
    .post-details .post-header .title {
        font-weight: 700;
    }
}

@media (min-width: 992px) {
    .post-details .post-header .title {
        font-size: 46px;
        line-height: 55px;
    }
}

.post-details .post-content {
    align-items: start;
}

.post-details .post-content .entry-content p {
    margin-bottom: 40px;
}
.text--base {
    color: #1c2d41;
}

.admin-fa_icon span.opacity-7.text-muted .fa,
.admin-fa_icon span.opacity-7.text-muted .fas,
.admin-fa_icon span.opacity-7.text-muted .far,
.admin-fa_icon span.opacity-7.text-muted .feather {
    color: #6777ef !important;
}

@media (max-width: 375px) {
    .admin-fa_icon .card .card-body {
        padding: 25px 12px;
    }
}

.admin-fa_icon h5.card-title {
    font-weight: 500;
    display: inline-block;
    font-size: 20px;
    color: #34395e;
}
.bg-white {
    background: #fff;
}

.border-eee {
    border: 1px solid #eee !important;
}
.border-top-eee {
    border-top: 1px solid #eee !important;
}

/*WA chat Css Start*/

/*WA CHat Box*/

.report .card {
    border: none;
    border-radius: 15px;
    background-image: linear-gradient(
            rgb(193 165 137 / 55%),
            rgb(193 165 137 / 55%)
        ),
        url(../../../assets/uploads/logo/wa-white.jpg);
    background-repeat: initial;
}
.report img {
    border-radius: 20px;
}

.report .adiv {
    background: rgb(132 132 132 / 80%);
    border-radius: 15px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    font-size: 12px;
    height: 46px;
}
.report .adiv p {
    font-size: 18px;
    margin-top: 0;
}

.report .chat {
    border: none;
    background-color: #fffafa;
    font-size: 14px;
    border-radius: 5px;
    color: #635a5a;
    line-height: 1.8;
}

.report .bg-white {
    border: 1px solid #d1c2b2;
    border-bottom: 2px solid #d1c2b2;
    font-size: 14px;
    border-radius: 10px;
    background-color: #dcf8c6 !important;
}

.text-wa {
    color: #635a5a;
    line-height: 1.8;
    font-size: 14px;
}

.report .myvideo img {
    border-radius: 20px;
}

.writing-box {
    background: rgb(132 132 132 / 80%);
}

.writing-box .send {
    padding-right: 25px;
}

.report .writing-box .input--group.px-3 {
    padding: 25px 15px 0 20px !important;
    width: 100%;
}

.report .form--control {
    background: #fff;
    border-radius: 15px;
    min-height: 20px;
    max-height: 100px;
    overflow-x: hidden;
    overflow-y: auto;
    font-size: 15px;
    font-weight: 400;
    word-wrap: break-word;
    white-space: pre-wrap;
    outline: none;
    width: 100%;
    padding: 10px 20px;
}

.report .form-control:focus {
    box-shadow: none;
}

.report .form-control::placeholder {
    color: #4a4a4a !important;
}

.timmer {
    font-size: 10px;
    position: absolute;
    right: 0;
    bottom: 0;
    padding-right: 5px;
    padding-bottom: 5px;
    color: #888181;
}

.chat-length {
    min-height: 450px;
    max-height: 600px;
    overflow-x: hidden;
}

.type_msg {
    background-color: rgb(255 255 255 / 80%) !important;
    border: 0 !important;
    color: #4a4a4a !important;
    height: 60px !important;
    overflow-y: auto;
}
.type_msg:focus {
    box-shadow: none !important;
    outline: 0 !important;
}
.mw-130 {
    min-width: 130px;
}
.btn--success {
    background-color: #2ecc71 !important;
    border-color: #2ecc71 !important;
}

.border-success {
    border: 1px solid #22ca80 !important ;
}
.border-primary {
    border: 1px solid #5f76e8 !important ;
}

/* End WA chat*/

@media only screen and (max-width: 1169px) {
    #main-wrapper[data-layout="vertical"][data-sidebartype="mini-sidebar"]
        .topbar
        .top-navbar
        .navbar-header
        .logo-text {
        display: initial;
    }
}
@media only screen and (max-width: 767px) {
    .dark-logo {
        max-width: 200px !important;
    }
    .light-logo {
        max-width: 200px !important;
    }
    .navbar-collapse .navbar-nav .nav-item:last-child .btn {
        width: auto;
    }
}

.text--site {
    color: #1fd3c6 !important;
}
.w-150px {
    width: 150px;
}
.wh-200-150 {
    width: 200px;
    height: 150px;
}
/* new css */
.sidebar-nav #sidebarnav .sidebar-item.selected > .sidebar-link {
    background-image: var(--gradient);
}
.topbar .top-navbar .navbar-nav > .nav-item > .nav-link .notify-no {
    position: absolute;
    top: 18px;
    right: 9px;
    padding: 5px 2px;
    background: var(--primary);
    min-width: 20px;
    height: 20px;
}
svg.feather.feather-bell.svg-icon {
    font-size: 20px;
    color: #1fd3c6;
    font-weight: normal;
}
svg.feather.feather-search.form-control-icon {
    color: #1fd3c6;
}
svg.feather.feather-chevron-down.svg-icon {
    color: #1fd3c6;
}
.btn.btn-danger.rounded-circle.btn-circle {
    width: 35px;
    height: 35px;
    background: #1fd3c6;
    border: 1px solid;
    align-items: center;
    justify-content: center;
    display: flex;
}
.admin-fa_icon span.opacity-7.text-muted .fa-2x {
    font-size: 22px;
}

.admin-fa_icon span.opacity-7.text-muted .fa,
.admin-fa_icon span.opacity-7.text-muted .fas,
.admin-fa_icon span.opacity-7.text-muted .fab,
.admin-fa_icon span.opacity-7.text-muted .far,
.admin-fa_icon span.opacity-7.text-muted .feather {
    color: var(--primary) !important;
}
.color-secondary {
    color: #488ff9 !important;
}
.sidebar-nav #sidebarnav .sidebar-item .sidebar-link .hide-menu {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-top: 3px;
    width: 100%;
    font-size: 0.875rem;
    font-weight: 400;
}
#main-wrapper[data-layout="vertical"]
    .left-sidebar[data-sidebarbg="skin6"]
    .sidebar-nav
    ul
    .sidebar-item.selected
    > .sidebar-link
    .feather-icon,
#main-wrapper[data-layout="vertical"]
    .left-sidebar[data-sidebarbg="skin6"]
    .sidebar-nav
    ul
    .sidebar-item.selected
    > .sidebar-link
    i,
#main-wrapper[data-layout="horizontal"]
    .left-sidebar[data-sidebarbg="skin6"]
    .sidebar-nav
    ul
    .sidebar-item.selected
    > .sidebar-link
    .feather-icon,
#main-wrapper[data-layout="horizontal"]
    .left-sidebar[data-sidebarbg="skin6"]
    .sidebar-nav
    ul
    .sidebar-item.selected
    > .sidebar-link
    i {
    color: #fff !important;
}
.dashboard__card .font-weight-medium {
    font-weight: 500 !important;
    font-size: 1.25rem !important;
}
.statistics__box {
    height: 95% !important;
}
.bd-callout {
    padding: 1.25rem;
    margin-top: 1.25rem;
    margin-bottom: 1.25rem;
    border: 1px solid whitesmoke;
    border-left-width: 0.25rem;
    border-radius: 0.25rem;
    background: #fff;
}
.bd-callout-warning {
    border-left-color: var(--primary) !important;
}
.bgGateway {
    background-color: var(--white);
    box-shadow: var(--shadow);
    position: relative;
    border-radius: 10px;
    padding: 30px;
    z-index: 1;
    border: none;
}
.preview-form .input-box .form-control {
    height: 50px;
    border-radius: 5px;
    background-color: #f5f5f5;
    padding: 10px 15px;
    font-weight: normal;
    font-size: 16px;
    caret-color: var(--primary);
}
.lang-select2 ul li img {
    width: 30px !important;
    height: 21px !important;
}
