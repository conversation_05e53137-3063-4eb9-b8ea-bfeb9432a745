@extends($theme.'layouts.merchant')
@section('title', trans($title))

@section('content')
    <div class="container-fluid">
        <div class="row ">
            @foreach($gateways as $key => $gateway)
                <div class="col-xl-2 col-lg-3 col-md-4  col-sm-6 ">
                    <div class="card shadow gateway text-center mb-3 overview-list ">
                        <div class="card-body p-4">
                            <img class="img-fluid gateway"
                                 src="{{ getFile(config('location.withdraw.path').$gateway->image)}}"
                                 alt="{{$gateway->name}}">
                            <button type="button"
                                    data-id="{{$gateway->id}}"
                                    data-name="{{$gateway->name}}"
                                    data-min_amount="{{getAmount($gateway->minimum_amount, $basic->fraction_number)}}"
                                    data-max_amount="{{getAmount($gateway->maximum_amount,$basic->fraction_number)}}"
                                    data-percent_charge="{{getAmount($gateway->percent_charge,$basic->fraction_number)}}"
                                    data-fix_charge="{{getAmount($gateway->fixed_charge, $basic->fraction_number)}}"
                                    data-toggle="modal" data-target="#addFundModal"
                                    class="btn btn-primary p-2 w-100 addFund lh-25 mt-2"
                                    data-backdrop='static' data-keyboard='false'>
                                @lang('Payout Now')
                            </button>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>


    @push('loadModal')
        <div class="modal fade addFundModal" id="addFundModal" tabindex="-1" role="dialog"
             aria-labelledby="exampleModalLabel"
             aria-hidden="true" data-keyboard="false"
             data-backdrop="static">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title method-name" id="planModalLabel"></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true" class="white-text">&times;</span>
                        </button>
                    </div>

                    <form action="{{route('user.payout.moneyRequest')}}" method="post">
                        @csrf
                        <div class="modal-body">
                            <div class="payment-form">
                                <p class="depositLimit"></p>
                                <p class="depositCharge"></p>

                                <div class="form-group">
                                    <label>@lang('Select Wallet')</label>
                                    <div class="input-group">
                                        <select name="wallet_type" class="form-control">
                                            <option value="balance" class="text-dark">@lang('Deposit Balance - '.$basic->currency_symbol.getAmount(auth()->user()->balance))</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>@lang('Amount')</label>
                                    <div class="input-group">
                                        <input type="text" class="amount form-control form-control-lg" name="amount" value="{{old('amount')}}">
                                        <div class="input-group-append">
                                            <span class="input-group-text bg-dark text-white show-currency"></span>
                                        </div>
                                    </div>
                                    <pre class="text-danger errors text-start ps-5"></pre>
                                </div>

                                <input type="hidden" class="gateway" name="gateway" value="">

                            </div>
                        </div>

                        <div class="modal-footer border-top-0">
                            <div class="continue-button large-button w-100">
                                <button type="submit" class="btn btn-primary checkCalc w-100 lh-35">@lang('Next')<i
                                        class="la la-arrow-right"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    @endpush

@endsection



@push('script')

    @if(count($errors) > 0 )
        <script>
            $(function () {
                $('[data-toggle="tooltip"]').tooltip()
            })
            @foreach($errors->all() as $key => $error)
            Notiflix.Notify.Failure("@lang($error)");
            @endforeach
        </script>
    @endif

    <script>
        "use strict";
        var id, minAmount, maxAmount, baseSymbol, fixCharge, percentCharge, currency, gateway;

        $('.addFund').on('click', function () {
            id = $(this).data('id');
            gateway = $(this).data('gateway');
            minAmount = $(this).data('min_amount');
            maxAmount = $(this).data('max_amount');
            baseSymbol = "{{config('basic.currency_symbol')}}";
            fixCharge = $(this).data('fix_charge');
            percentCharge = $(this).data('percent_charge');
            currency = $(this).data('currency');
            $('.depositLimit').text(`{{__('Transaction Limit:')}} ${minAmount} - ${maxAmount}  ${baseSymbol}`);

            var depositCharge = `{{__('Charge:')}} ${fixCharge} ${baseSymbol}  ${(0 < percentCharge) ? ' + ' + percentCharge + ' % ' : ''}`;
            $('.depositCharge').text(depositCharge);
            $('.method-name').text(`{{__('Payout By')}} ${$(this).data('name')}`);
            $('.show-currency').text("{{config('basic.currency')}}");
            $('.gateway').val(id);
        });

        $('.close').on('click', function (e) {
            $('#loading').hide();
            $('.amount').val(``);
            $("#addFundModal").modal("hide");
        });
    </script>
@endpush

