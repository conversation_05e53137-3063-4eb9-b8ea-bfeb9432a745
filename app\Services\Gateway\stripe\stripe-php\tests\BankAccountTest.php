<?php

namespace StripeJS;

class BankAccountTest extends TestCase
{
    public function testVerify()
    {
        self::authorizeFromEnv();

        $customer = self::createTestCustomer();

        $bankAccount = $customer->sources->create(array(
            'source' => array(
                'object' => 'bank_account',
                'account_holder_type' => 'individual',
                'account_number' => '************',
                'account_holder_name' => 'John Doe',
                'routing_number' => '*********',
                'country' => 'US'
            )
        ));

        $this->assertSame($bankAccount->status, 'new');

        $bankAccount = $bankAccount->verify(array(
            'amounts' => array(32, 45)
        ));

        $this->assertSame($bankAccount->status, 'verified');
    }
}
