<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAgentTransfersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('agent_transfers', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('sender_id')->unsigned();
            $table->bigInteger('receiver_id')->unsigned();
            $table->decimal('amount', 18, 8);
            $table->decimal('fee', 18, 8);
            $table->decimal('final_amount', 18, 8);
            $table->text('note')->nullable();
            $table->string('trx_id', 40)->unique();
            $table->tinyInteger('status')->default(1)->comment('1 = Completed, 0 = Failed');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('agent_transfers');
    }
}
