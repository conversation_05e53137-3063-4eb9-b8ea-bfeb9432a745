<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class apiKYC
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $basic = basicControl();
        if($basic->identity_verification == 1 && Auth::user()->identity_verify != '2'){
            $result['status'] = false;
            $result['message'] = 'Identity Verification Required';
            $result['data'] = [];
            return response($result, 200);
        }

        if($basic->address_verification == 1 && Auth::user()->address_verify != '2'){
            $result['status'] = false;
            $result['message'] = 'Address Verification Required';
            $result['data'] = [];
            return response($result, 200);
        }
        return $next($request);
    }
}
