<?php

namespace App\Services\Gateway\paystrix;

use App\Models\Fund;
use Facades\App\Services\BasicCurl;
use Facades\App\Services\BasicService;
use Illuminate\Support\Facades\Log;

class Payment
{
    public static function prepareData($order, $gateway)
    {
        $basic = (object) config('basic');
        
        // Prepare the checkout URL with required parameters
        $baseUrl = $gateway->parameters->base_url ?? 'https://example.com/checkout';
        
        // Build the query parameters
        $queryParams = [
            'checkout_id' => 'custom-' . $order->transaction, // Required: The checkout ID with custom- prefix
            'price' => round($order->final_amount, 2), // Required: The price amount
            'external-reference' => $order->transaction, // External reference for tracking
            'redirect' => route('success'), // Redirect URL after successful payment
            'currency' => $order->gateway_currency, // The checkout currency
            'type' => 'link', // Set checkout type (inline, link, popup, hidden)
            'note' => "Payment to {$basic->site_title}" // Add a note to the transaction
        ];
        
        // Build the checkout URL
        $checkoutUrl = $baseUrl . '?' . http_build_query($queryParams);
        
        $send['redirect'] = true;
        $send['redirect_url'] = $checkoutUrl;
        
        return json_encode($send);
    }

    public static function ipn($request, $gateway, $order = null, $trx = null, $type = null)
    {
        Log::info('PayStrix IPN', $request->all());
        
        // Verify the webhook signature if provided
        $webhookKey = $gateway->parameters->webhook_key ?? '';
        
        // Process the webhook data
        if (isset($request->transaction)) {
            
            
            if (!empty($webhookKey)) {
                // Implement signature verification if needed
                 if ($webhookKey !== $request->key) { 
                    return false; 
                }
            }
            
            //$transaction = $request->transaction;
            $transaction = json_decode(json_encode($request->transaction));
            Log::info('Transaction :'. json_encode($transaction));

            // Extract the transaction ID from checkout_id (remove 'custom-' prefix)
            $transactionId = str_replace('custom-', '', $transaction->id);
            Log::info('transaction id: '. json_encode($transactionId));

            // Find the order by transaction ID
            $order = Fund::where('transaction', $transaction->external_reference)
                        ->orWhere('transaction', $transactionId)
                        ->orderBy('id', 'DESC')
                        ->first();
            
            if ($order) {
                Log::info('order: '. json_encode($order));

                // Verify payment details
                //$isValidAmount = $transaction->amount == $order->final_amount;
                $isValidCurrency = strtolower($transaction->currency) == strtolower($order->gateway_currency);
                $isCompleted = $transaction->status == 'C'; // Assuming 'C' means completed
                
                if ( $isValidCurrency && $isCompleted && $order->status == 0) {//$isValidAmount &&
                    Log::info('order is valid');
                    // Update payment status
                    BasicService::preparePaymentUpgradation($order);
                    $data['status'] = 'success';
                    $data['msg'] = 'Transaction was successful.';
                    $data['redirect'] = route('success');
                } else {
                    $data['status'] = 'error';
                    $data['msg'] = 'Invalid payment data.';
                    $data['redirect'] = route('failed');
                }

            } else {
                Log::info('Order not found.');
                $data['status'] = 'error';
                $data['msg'] = 'Order not found.';
                $data['redirect'] = route('failed');
            }
        } else {
            Log::info('Invalid webhook data.');
            $data['status'] = 'error';
            $data['msg'] = 'Invalid webhook data.';
            $data['redirect'] = route('failed');
        }
        
        return $data;
    }
}
