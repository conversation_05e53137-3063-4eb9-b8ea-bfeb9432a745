<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Traits\ApiValidation;
use App\Http\Traits\Notify;
use App\Http\Traits\Upload;
use App\Models\Fund;
use App\Models\Gateway;
use App\Models\SendMoney;
use Carbon\Carbon;
use Facades\App\Services\BasicService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class PaymentController extends Controller
{
    use ApiValidation, Upload, Notify;

    public function paymentGateways()
    {
        try {
            $data['baseCurrency'] = config('basic.currency');
            $data['baseSymbol'] = config('basic.currency_symbol');
            $data['gateways'] = Gateway::where('status', 1)->orderBy('sort_by', 'ASC')->get()->map(function ($query) {
                $query->image = getFile(config('location.gateway.path') . $query->image);
                return $query;
            });

            $result['status'] = true;
            $result['data'] = $data;
            return response($result, 200);
        } catch (\Exception $e) {
            $result['status'] = false;
            $result['message'] = 'Invalid Error';
            return response($result, 200);
        }
    }

    public function manualPaymentSubmit(Request $request)
    {
        $validator = validator()->make($request->all(), [
            'gateway' => 'required',
            'invoice' => 'required'
        ]);

        if ($validator->fails()) {
            $result['status'] = false;
            $result['message'] = $validator->errors();
            return response($result, 200);
        }

        try {
            $basic = (object)config('basic');

            $sendMoney = SendMoney::latest()->where(['invoice' => $request->invoice, 'status' => 2, 'payment_status' => 0])->with(['sendCurrency:id,name,rate'])->first();
            if (!$sendMoney) {
                $result['status'] = false;
                $result['message'] = 'This transaction is not valid to make payment';
                return response($result, 200);
            }

            $totalPayment = getAmount($sendMoney->totalBaseAmountPay);
            $gate = Gateway::where('id', $request->gateway)->where('status', 1)->first();
            if (!$gate) {
                $result['status'] = false;
                $result['message'] = 'Invalid Gateway';
                return response($result, 200);
            }

            $reqAmount = $totalPayment;
            if ($gate->min_amount > $reqAmount || $gate->max_amount < $reqAmount) {
                $result['status'] = false;
                $result['message'] = 'Please Follow Transaction Limit';
                return response($result, 200);
            }

            $charge = getAmount($gate->fixed_charge + ($reqAmount * $gate->percentage_charge / 100));
            $payable = getAmount($reqAmount + $charge);
            $final_amo = getAmount($payable * $gate->convention_rate);
            $user = auth()->user();

            DB::beginTransaction();

            $fund = $this->newFund($request, $user, $gate, $charge, $final_amo, $reqAmount, $sendMoney->id);

            $data = Fund::where('transaction', $fund['transaction'])->orderBy('id', 'DESC')->with(['gateway', 'user'])->first();
            if (is_null($data)) {
                $result['status'] = false;
                $result['message'] = 'Invalid Fund Request';
                return response($result, 200);
            }
            if ($data->status != 0) {
                $result['status'] = false;
                $result['message'] = 'Invalid Fund Request';
                return response($result, 200);
            }

            $gateway = $data->gateway;
            $params = optional($data->gateway)->parameters;

            $rules = [];
            $inputField = [];

            $verifyImages = [];

            if ($params != null) {
                foreach ($params as $key => $cus) {
                    $rules[$key] = [$cus->validation];
                    if ($cus->type == 'file') {
                        array_push($rules[$key], 'image');
                        array_push($rules[$key], 'mimes:jpeg,jpg,png');
//                        array_push($rules[$key], 'max:2048');
                        array_push($verifyImages, $key);
                    }
                    if ($cus->type == 'text') {
                        array_push($rules[$key], 'max:191');
                    }
                    if ($cus->type == 'textarea') {
                        array_push($rules[$key], 'max:300');
                    }
                    $inputField[] = $key;
                }
            }

            $validator = validator()->make($request->all(), $rules);

            if ($validator->fails()) {
                return $this->withErrors(collect($validator->messages())->collapse()[0]);
            }

            $path = config('location.deposit.path') . date('Y') . '/' . date('m') . '/' . date('d');
            $collection = collect($request);

            $reqField = [];
            if ($params != null) {
                foreach ($collection as $k => $v) {
                    foreach ($params as $inKey => $inVal) {
                        if ($k != $inKey) {
                            continue;
                        } else {
                            if ($inVal->type == 'file') {
                                if ($request->hasFile($inKey)) {
                                    try {
                                        $reqField[$inKey] = [
                                            'field_name' => $this->uploadImage($request[$inKey], $path),
                                            'type' => $inVal->type,
                                        ];
                                    } catch (\Exception $exp) {
                                        return $this->withErrors('Could not upload your ' . $inKey);
                                    }
                                }
                            } else {
                                $reqField[$inKey] = [
                                    'field_name' => $v,
                                    'type' => $inVal->type,
                                ];
                            }
                        }
                    }
                }
                $data->detail = $reqField;
            } else {
                $data->detail = null;
            }

            $data->created_at = Carbon::now();
            $data->status = 2; // pending
            $data->update();
            DB::commit();
            $sendmoney = $data->sendmoney;
            $sendmoney->payment_status = 3;
            $sendmoney->save();

            $msg = [
                'username' => optional($data->user)->username,
                'amount' => getAmount($sendmoney->totalPay, config('basic.fraction_number')),
                'currency' => $sendmoney->send_curr
            ];
            $action = [
                "link" => route('admin.money-transfer.details', $sendmoney),
                "icon" => "fa fa-money-bill-alt text-white"
            ];
            $this->adminPushNotification('SEND_MONEY_REQUEST', $msg, $action);

            $result['status'] = true;
            $result['data'] = 'You request has been taken.';
            return response($result, 200);

        } catch (\Exception $e) {
            DB::rollBack();
            $result['status'] = false;
            $result['message'] = $e->getMessage();
            return response($result, 200);
        }
    }

    public function paymentDone(Request $request)
    {
        try {
            $sendMoney = SendMoney::latest()->where(['invoice' => $request->invoice, 'status' => 2, 'payment_status' => 0])->first();
            $gate = Gateway::where('id', $request->gateway)->where('status', 1)->first();
            if ($sendMoney && $gate) {
                $reqAmount = getAmount($sendMoney->totalBaseAmountPay);
                $charge = getAmount($gate->fixed_charge + ($reqAmount * $gate->percentage_charge / 100));
                $payable = getAmount($reqAmount + $charge);
                $final_amo = getAmount($payable * $gate->convention_rate);
                $user = auth()->user();

                $fund = $this->newFund($request, $user, $gate, $charge, $final_amo, $reqAmount, $sendMoney->id);

                BasicService::preparePaymentUpgradation($fund);
                $result['status'] = true;
                $result['data'] = 'Payment has been completed';
                return response($result, 200);
            }

            $result['status'] = false;
            $result['message'] = 'Record not found';
            return response($result, 200);
        } catch (\Exception $e) {
            $result['status'] = false;
            $result['message'] = $e->getMessage();
            return response($result, 200);
        }
    }


    public function cardPayment(Request $request)
    {
        $validateUser = Validator::make($request->all(),
            [
                'invoice' => 'required',
                'gateway' => 'required'
            ]);

        if ($validateUser->fails()) {
            $result['status'] = false;
            $result['message'] = $validateUser->errors();
            return response($result, 200);
        }

        $sendMoney = SendMoney::latest()->where(['invoice' => $request->invoice, 'status' => 2, 'payment_status' => 0])->with(['sendCurrency:id,name,rate'])->first();
        if (!$sendMoney) {
            $result['status'] = false;
            $result['message'] = 'This transaction is not valid to make payment';
            return response($result, 200);
        }

        $totalPayment = getAmount($sendMoney->totalBaseAmountPay);
        $gate = Gateway::where('id', $request->gateway)->where('status', 1)->first();
        if (!$gate) {
            $result['status'] = false;
            $result['message'] = 'Invalid Gateway';
            return response($result, 200);
        }

        $reqAmount = $totalPayment;
        if ($gate->min_amount > $reqAmount || $gate->max_amount < $reqAmount) {
            $result['status'] = false;
            $result['message'] = 'Please Follow Transaction Limit';
            return response($result, 200);
        }

        $charge = getAmount($gate->fixed_charge + ($reqAmount * $gate->percentage_charge / 100));
        $payable = getAmount($reqAmount + $charge);
        $final_amo = getAmount($payable * $gate->convention_rate);
        $user = auth()->user();


        $fund = $this->newFund($request, $user, $gate, $charge, $final_amo, $reqAmount, $sendMoney->id);

        $data = Fund::where('transaction', $fund['transaction'])->orderBy('id', 'DESC')->with(['gateway', 'user'])->first();
        if (is_null($data)) {
            $result['status'] = false;
            $result['message'] = 'Invalid Fund Request';
            return response($result, 200);
        }
        if ($data->status != 0) {
            $result['status'] = false;
            $result['message'] = 'Invalid Fund Request';
            return response($result, 200);
        }

        $getwayObj = 'App\\Services\\Gateway\\' . $gate->code . '\\Payment';
        $data = $getwayObj::mobileIpn($request, $gate, $data);
        if ($data == 'success') {
            $result['status'] = true;
            $result['message'] = 'Payment has been complete';
            return response($result, 200);
        } else {
            $result['status'] = false;
            $result['message'] = 'unsuccessful transaction.';
            return response($result, 200);
        }
    }

    public function newFund(Request $request, $user, $gate, $charge, $final_amo, $amount, $sendMoneyId = null): Fund
    {
        $fund = new Fund();
        $fund->user_id = $user->id;
        $fund->gateway_id = $gate->id;
        $fund->send_money_id = $sendMoneyId;
        $fund->gateway_currency = strtoupper($gate->currency);
        $fund->amount = $amount;
        $fund->charge = $charge;
        $fund->rate = $gate->convention_rate;
        $fund->final_amount = getAmount($final_amo);
        $fund->btc_amount = 0;
        $fund->btc_wallet = "";
        $fund->transaction = strRandom();
        $fund->try = 0;
        $fund->status = 0;
        $fund->save();
        return $fund;
    }

    public function showOtherPayment(Request $request)
    {
        $validateUser = Validator::make($request->all(),
            [
                'invoice' => 'required',
                'gateway' => 'required'
            ]);

        if ($validateUser->fails()) {
            $result['status'] = false;
            $result['message'] = $validateUser->errors();
            return response($result, 200);
        }

        $sendMoney = SendMoney::latest()->where(['invoice' => $request->invoice, 'status' => 2, 'payment_status' => 0])->with(['sendCurrency:id,name,rate'])->first();
        if (!$sendMoney) {
            $result['status'] = false;
            $result['message'] = 'This transaction is not valid to make payment';
            return response($result, 200);
        }

        $totalPayment = getAmount($sendMoney->totalBaseAmountPay);
        $gate = Gateway::where('id', $request->gateway)->where('status', 1)->first();
        if (!$gate) {
            $result['status'] = false;
            $result['message'] = 'Invalid Gateway';
            return response($result, 200);
        }

        $reqAmount = $totalPayment;
        if ($gate->min_amount > $reqAmount || $gate->max_amount < $reqAmount) {
            $result['status'] = false;
            $result['message'] = 'Please Follow Transaction Limit';
            return response($result, 200);
        }

        $charge = getAmount($gate->fixed_charge + ($reqAmount * $gate->percentage_charge / 100));
        $payable = getAmount($reqAmount + $charge);
        $final_amo = getAmount($payable * $gate->convention_rate);
        $user = auth()->user();


        $fund = $this->newFund($request, $user, $gate, $charge, $final_amo, $reqAmount, $sendMoney->id);

        $data = Fund::where('transaction', $fund['transaction'])->orderBy('id', 'DESC')->with(['gateway', 'user'])->first();
        if (is_null($data)) {
            $result['status'] = false;
            $result['message'] = 'Invalid Fund Request';
            return response($result, 200);
        }
        if ($data->status != 0) {
            $result['status'] = false;
            $result['message'] = 'Invalid Fund Request';
            return response($result, 200);
        }

        $val['url'] = route('paymentView', $fund->transaction);
        $result['status'] = true;
        $result['data'] = $val;
        return response($result, 200);
    }

    public function paymentView($utr)
    {
        $order = Fund::latest()->where('transaction', $utr)->first();
        try {
            if ($order) {
                $getwayObj = 'App\\Services\\Gateway\\' . $order->gateway->code . '\\Payment';
                $data = $getwayObj::prepareData($order, $order->gateway, true);
                $data = json_decode($data);

                if (isset($data->error)) {
                    $result['status'] = false;
                    $result['message'] = $data->message;
                    return response($result, 200);
                }

                if (isset($data->redirect)) {
                    return redirect($data->redirect_url);
                }

                if ($data->view) {
                    $parts = explode(".", $data->view);
                    $desiredValue = end($parts);
                    $newView = 'mobile-payment.' . $desiredValue;
                    return view($newView, compact('data', 'order'));
                }

                abort(404);
            }
        } catch (\Exception $e) {
            $result['status'] = false;
            $result['message'] = $e->getMessage();
            return response($result, 200);
        }
    }
}
