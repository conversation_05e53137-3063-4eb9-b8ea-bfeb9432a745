@extends('admin.layouts.app')
@section('title')
    @lang($page_title)
@endsection
@section('content')
    <div class="page-header card card-primary m-0 m-md-4 my-4 m-md-0 p-5 shadow">
        <form action="{{ route('admin.agent-transfers.search') }}" method="get">
            <div class="row justify-content-between align-items-center">
                <div class="col-md-4">
                    <div class="form-group">
                        <input type="text" name="sender" value="{{@request()->sender}}" class="form-control"
                               placeholder="@lang('Sender')">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <input type="text" name="receiver" value="{{@request()->receiver}}" class="form-control"
                               placeholder="@lang('Receiver')">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <input type="text" name="trx_id" value="{{@request()->trx_id}}" class="form-control"
                               placeholder="@lang('Transaction ID')">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <input type="date" class="form-control" name="date_time" id="datepicker"/>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <select name="status" class="form-control">
                            <option value="">@lang('All Status')</option>
                            <option value="1"
                                    @if(@request()->status == '1') selected @endif>@lang('Completed')</option>
                            <option value="0"
                                    @if(@request()->status == '0') selected @endif>@lang('Failed')</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <button type="submit" class="btn btn-block btn-primary"><i
                                class="fas fa-search"></i> @lang('Search')</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="card card-primary m-0 m-md-4 my-4 m-md-0 shadow">
        <div class="card-body">
            <div class="table-responsive">
                <table class="categories-show-table table table-hover table-striped table-bordered">
                    <thead class="thead-dark">
                    <tr>
                        <th>@lang('Date')</th>
                        <th>@lang('TRX ID')</th>
                        <th>@lang('Sender')</th>
                        <th>@lang('Receiver')</th>
                        <th>@lang('Amount')</th>
                        <th>@lang('Fee')</th>
                        <th>@lang('Final Amount')</th>
                        <th>@lang('Status')</th>
                        <th>@lang('Action')</th>
                    </tr>
                    </thead>
                    <tbody>
                    @forelse($transfers as $transfer)
                        <tr>
                            <td data-label="@lang('Date')">{{ dateTime($transfer->created_at) }}</td>
                            <td data-label="@lang('TRX ID')">{{ $transfer->trx_id }}</td>
                            <td data-label="@lang('Sender')">
                                <a href="{{ route('admin.user-edit', $transfer->sender_id) }}" target="_blank">
                                    {{ optional($transfer->sender)->username }}
                                </a>
                            </td>
                            <td data-label="@lang('Receiver')">
                                <a href="{{ route('admin.user-edit', $transfer->receiver_id) }}" target="_blank">
                                    {{ optional($transfer->receiver)->username }}
                                </a>
                            </td>
                            <td data-label="@lang('Amount')">{{ getAmount($transfer->amount) }} {{ config('basic.currency') }}</td>
                            <td data-label="@lang('Fee')">{{ getAmount($transfer->fee) }} {{ config('basic.currency') }}</td>
                            <td data-label="@lang('Final Amount')">{{ getAmount($transfer->final_amount) }} {{ config('basic.currency') }}</td>
                            <td data-label="@lang('Status')">
                                @if($transfer->status == 1)
                                    <span class="badge badge-success">@lang('Completed')</span>
                                @else
                                    <span class="badge badge-danger">@lang('Failed')</span>
                                @endif
                            </td>
                            <td data-label="@lang('Action')">
                                <a href="{{ route('admin.agent-transfers.details', $transfer->id) }}"
                                   class="btn btn-sm btn-primary">
                                    <i class="fa fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="9">@lang('No Data Found')</td>
                        </tr>
                    @endforelse
                    </tbody>
                </table>
                {{ $transfers->appends($_GET)->links() }}
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script>
        'use strict'
        $(document).ready(function () {
            $('select[name=status]').select2({
                selectOnClose: true
            });
        });
    </script>
@endpush
