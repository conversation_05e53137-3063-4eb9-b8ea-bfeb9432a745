<?php

namespace Tests\Feature;

use App\Models\SendMoney;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ExpireUnpaidRemittancesTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_expires_unpaid_remittances_older_than_30_minutes()
    {
        // Create a remittance that is 35 minutes old (should be expired)
        $oldRemittance = SendMoney::factory()->create([
            'status' => 2, // pending
            'payment_status' => 0, // unpaid
            'created_at' => Carbon::now()->subMinutes(35),
        ]);

        // Create a remittance that is 25 minutes old (should not be expired)
        $newRemittance = SendMoney::factory()->create([
            'status' => 2, // pending
            'payment_status' => 0, // unpaid
            'created_at' => Carbon::now()->subMinutes(25),
        ]);

        // Run the command
        $this->artisan('remittance:expire-unpaid');

        // Refresh the models from the database
        $oldRemittance->refresh();
        $newRemittance->refresh();

        // Assert the old remittance was expired
        $this->assertEquals(3, $oldRemittance->status); // status should be 3 (cancelled)
        $this->assertEquals('Automatically cancelled due to payment timeout (30 minutes)', $oldRemittance->admin_reply);

        // Assert the new remittance was not expired
        $this->assertEquals(2, $newRemittance->status); // status should still be 2 (pending)
        $this->assertNull($newRemittance->admin_reply);
    }
}
