@extends('admin.layouts.app')
@section('title')
    @lang($page_title)
@endsection
@section('content')
    <div class="card card-primary m-0 m-md-4 my-4 m-md-0 shadow">
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <h4 class="card-title mb-4">@lang('Agent Transfer Details')</h4>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th>@lang('Transaction ID')</th>
                                <td>{{ $transfer->trx_id }}</td>
                            </tr>
                            <tr>
                                <th>@lang('Date')</th>
                                <td>{{ dateTime($transfer->created_at) }}</td>
                            </tr>
                            <tr>
                                <th>@lang('Status')</th>
                                <td>
                                    @if($transfer->status == 1)
                                        <span class="badge badge-success">@lang('Completed')</span>
                                    @else
                                        <span class="badge badge-danger">@lang('Failed')</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title">@lang('Sender Information')</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tr>
                                        <th>@lang('Name')</th>
                                        <td>{{ optional($transfer->sender)->fullname }}</td>
                                    </tr>
                                    <tr>
                                        <th>@lang('Username')</th>
                                        <td>
                                            <a href="{{ route('admin.user-edit', $transfer->sender_id) }}" target="_blank">
                                                {{ optional($transfer->sender)->username }}
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>@lang('Email')</th>
                                        <td>{{ optional($transfer->sender)->email }}</td>
                                    </tr>
                                    <tr>
                                        <th>@lang('Phone')</th>
                                        <td>{{ optional($transfer->sender)->phone }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title">@lang('Receiver Information')</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tr>
                                        <th>@lang('Name')</th>
                                        <td>{{ optional($transfer->receiver)->fullname }}</td>
                                    </tr>
                                    <tr>
                                        <th>@lang('Username')</th>
                                        <td>
                                            <a href="{{ route('admin.user-edit', $transfer->receiver_id) }}" target="_blank">
                                                {{ optional($transfer->receiver)->username }}
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>@lang('Email')</th>
                                        <td>{{ optional($transfer->receiver)->email }}</td>
                                    </tr>
                                    <tr>
                                        <th>@lang('Phone')</th>
                                        <td>{{ optional($transfer->receiver)->phone }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title">@lang('Transfer Information')</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tr>
                                        <th>@lang('Amount')</th>
                                        <td>{{ getAmount($transfer->amount) }} {{ config('basic.currency') }}</td>
                                    </tr>
                                    <tr>
                                        <th>@lang('Fee')</th>
                                        <td>{{ getAmount($transfer->fee) }} {{ config('basic.currency') }}</td>
                                    </tr>
                                    <tr>
                                        <th>@lang('Final Amount')</th>
                                        <td>{{ getAmount($transfer->final_amount) }} {{ config('basic.currency') }}</td>
                                    </tr>
                                    <tr>
                                        <th>@lang('Note')</th>
                                        <td>{{ $transfer->note ?? 'N/A' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <a href="{{ route('admin.agent-transfers') }}" class="btn btn-primary">
                        <i class="fa fa-arrow-left"></i> @lang('Back')
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection
