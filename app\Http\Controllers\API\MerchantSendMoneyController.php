<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\SendMoney;
use App\Models\Country;
use App\Models\Service;
use App\Models\CountryService;
use App\Models\Coupon;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Traits\Notify;
use Facades\App\Services\BasicService;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use <PERSON><PERSON><PERSON>\Purify\Facades\Purify;

class MerchantSendMoneyController extends Controller
{
    use Notify;

    /**
     * Send money/remittance via API for merchants
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendMoney(Request $request)
    {
        $user = auth()->user();

        // Merchant authentication and status checks
        if ($user->merchant == 0) {
            return response()->json([
                'status' => false,
                'message' => 'Unauthorized merchant access'
            ], 403);
        }

        if ($user->status == 0) {
            return response()->json([
                'status' => false,
                'message' => 'Merchant is not active!'
            ], 403);
        }

        // Check if merchant has a country assigned
        if (!$user->country_id) {
            return response()->json([
                'status' => false,
                'message' => 'You need to have a country assigned to your merchant account. Please contact support.'
            ], 403);
        }

        // Validation rules based on existing merchantSendMoneyFormData logic
        $validator = validator()->make($request->all(), [
            // Transaction details
            'send_currency_id' => 'required|exists:countries,id',
            'receive_currency_id' => 'required|exists:countries,id',
            'service_id' => 'required|exists:services,id',
            'country_service_id' => 'required|exists:country_services,id',
            'send_amount' => 'required|numeric|min:1',
            'payment_type' => ['required', Rule::in(['fund'])], // Only fund payment for API
            
            // Sender information
            'first_name' => 'required|max:91',
            'last_name' => 'required|max:91',
            'sender_phone' => 'required|max:50',
            'sender_email' => 'nullable|email|max:50',
            'sender_address' => 'nullable|max:191',
            'sender_city' => 'nullable|max:40',
            'sender_post_code' => 'nullable|max:20',
            'sender_country' => 'nullable|max:20',
            
            // Recipient information
            'recipient_name' => 'required|max:91',
            'recipient_contact_no' => 'required|max:20',
            'recipient_email' => 'nullable|email|max:30',
            'fund_source' => 'nullable|max:255',
            'purpose' => 'nullable|max:255',
            'promo_code' => 'nullable|numeric',
            
            // Identity verification
            'identity_type' => ['nullable', Rule::in(['Driving License','Passport','National ID'])],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            DB::beginTransaction();

            // Validate currencies and services
            $sendCountry = Country::where('id', $request->send_currency_id)
                ->where('send_from', 1)
                ->where('status', 1)
                ->first();

            if (!$sendCountry) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid sending country'
                ], 400);
            }

            // Check if merchant can send from this country (must be their assigned country)
            if ($sendCountry->id != $user->country_id) {
                return response()->json([
                    'status' => false,
                    'message' => 'You can only send money from your assigned country'
                ], 403);
            }

            $receiveCountry = Country::where('id', $request->receive_currency_id)
                ->where('send_to', 1)
                ->where('status', 1)
                ->first();

            if (!$receiveCountry) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid receiving country'
                ], 400);
            }

            // Validate service and provider
            $service = Service::where('id', $request->service_id)->where('status', 1)->first();
            if (!$service) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid service'
                ], 400);
            }

            $provider = CountryService::where('id', $request->country_service_id)
                ->where('country_id', $receiveCountry->id)
                ->where('service_id', $service->id)
                ->first();

            if (!$provider) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid service provider'
                ], 400);
            }

            // Calculate exchange rate and fees
            $amount = $request->send_amount;
            $rate = $receiveCountry->rate / $sendCountry->rate;
            $fees = getCharge($amount, $receiveCountry->id, $service->id);
            $totalPayable = $amount + $fees;
            $recipientGetAmount = $amount * $rate;

            // Round up (ceiling) the final amount as per requirements
            $totalPayable = ceil($totalPayable);

            // Create SendMoney record
            $invoice = invoice();
            $sendMoney = new SendMoney();
            $sendMoney->invoice = $invoice;
            $sendMoney->user_id = $user->id;
            $sendMoney->send_currency_id = $sendCountry->id;
            $sendMoney->receive_currency_id = $receiveCountry->id;
            $sendMoney->service_id = $service->id;
            $sendMoney->country_service_id = $provider->id;
            $sendMoney->send_curr_rate = $sendCountry->rate;
            $sendMoney->send_curr = $sendCountry->code;
            $sendMoney->receive_curr = $receiveCountry->code;
            $sendMoney->rate = $rate;
            $sendMoney->send_amount = $amount;
            $sendMoney->fees = $fees;
            $sendMoney->payable_amount = $totalPayable;
            $sendMoney->recipient_get_amount = $recipientGetAmount;

            // Set sender information
            $sendMoney->sender_name = $request->first_name . ' ' . $request->last_name;
            $sendMoney->sender_phone = $request->sender_phone;
            $sendMoney->sender_email = $request->sender_email;
            $sendMoney->sender_address = $request->sender_address;
            $sendMoney->sender_city = $request->sender_city;
            $sendMoney->sender_post_code = $request->sender_post_code;
            $sendMoney->sender_country = $request->sender_country;
            $sendMoney->sender_identity_type = $request->identity_type;

            // Set recipient information (ensure recipient name is saved as per requirements)
            $sendMoney->recipient_name = $request->recipient_name;
            $sendMoney->recipient_contact_no = $request->recipient_contact_no;
            $sendMoney->recipient_email = $request->recipient_email;
            $sendMoney->fund_source = $request->fund_source;
            $sendMoney->purpose = $request->purpose;

            $sendMoney->payment_type = $request->payment_type;

            // Handle promo code if provided
            if ($request->promo_code) {
                $coupon = Coupon::where('code', trim($request->promo_code))->whereNull('user_id')->first();
                if (!$coupon) {
                    DB::rollBack();
                    return response()->json([
                        'status' => false,
                        'message' => 'Invalid promo code'
                    ], 400);
                }

                $sendMoney->discount = ($sendMoney->payable_amount * $coupon->reduce_fee) / 100;
                $sendMoney->promo_code = $coupon->code;

                $coupon->user_id = $user->id;
                $coupon->used_at = Carbon::now();
                $coupon->update();
            }

            // Calculate merchant commission
            $merchantCom = 0;
            if (0 < $sendMoney->fees) {
                $basicCom = config('basic.merchant_commission'); // percent
                $baseCharge = $sendMoney->fees / $sendMoney->send_curr_rate;
                $merchantCom = ($baseCharge * $basicCom) / 100;

                $sendMoney->merchant_commission = $merchantCom;
                $sendMoney->admin_commission = ($baseCharge - $merchantCom);
            } else {
                $sendMoney->merchant_commission = 0;
                $sendMoney->admin_commission = 0;
            }

            // Check if merchant has sufficient balance for fund payment
            if ($request->payment_type == 'fund' && $sendMoney->totalBaseAmountPay > $user->balance) {
                DB::rollBack();
                return response()->json([
                    'status' => false,
                    'message' => 'Insufficient balance. Required: ' . getAmount($sendMoney->totalBaseAmountPay, config('basic.fraction_number'))
                ], 400);
            }

            // Set status to draft initially
            $sendMoney->status = 2; // Draft
            $sendMoney->save();

            // Process fund payment
            if ($request->payment_type == 'fund') {
                $sendMoney->payment_status = 1;
                $sendMoney->paid_at = Carbon::now();
                $sendMoney->save();

                // Deduct amount from merchant balance
                $user->balance -= $sendMoney->totalBaseAmountPay;
                $user->save();

                // Create transaction record for deduction
                $trx_id = strRandom();
                $remarks = "Send money Invoice: " . $sendMoney->invoice;
                BasicService::makeTransaction($user, getAmount($sendMoney->totalBaseAmountPay), getAmount($sendMoney->totalBaseAmountChargePay), '-', $trx_id, $remarks);

                // Add merchant commission to balance
                $remarks = "You got commission from #" . $sendMoney->invoice;
                BasicService::makeTransaction($user, getAmount($merchantCom), 0, '+', $trx_id, $remarks);

                // Send SMS notification to recipient as per requirements
                if (!empty($sendMoney->recipient_contact_no)) {
                    $this->sendSms($sendMoney->recipient_contact_no, 'REMITTANCE_SENT', [
                        'invoice' => $sendMoney->invoice
                    ], 4);
                }

                // Send notifications to admin
                $msg = [
                    'username' => $user->username,
                    'amount' => getAmount($sendMoney->totalPay, config('basic.fraction_number')),
                    'currency' => $sendMoney->send_curr
                ];
                $action = [
                    "link" => route('admin.money-transfer.details', $sendMoney),
                    "icon" => "fa fa-money-bill-alt text-white"
                ];
                $this->adminPushNotification('SEND_MONEY_REQUEST', $msg, $action);

                // Send notification to merchant
                $msg2 = [
                    'amount' => getAmount($sendMoney->totalPay, config('basic.fraction_number')),
                    'currency' => $sendMoney->send_curr,
                ];
                $action2 = [
                    "link" => '#',
                    "icon" => "fas fa-money-bill-alt text-white"
                ];
                $this->userPushNotification($user, 'MERCHANT_TRANSFER_PROCESSING', $msg2, $action2);

                // Send email/SMS to merchant
                $this->sendMailSms($user, 'MERCHANT_TRANSFER_PROCESSING', [
                    'amount' => getAmount($sendMoney->totalPay, config('basic.fraction_number')),
                    'currency' => $sendMoney->send_curr,
                    'invoice' => $sendMoney->invoice
                ]);
            }

            DB::commit();

            return response()->json([
                'status' => true,
                'message' => 'Remittance sent successfully',
                'data' => [
                    'invoice' => $sendMoney->invoice,
                    'send_amount' => getAmount($sendMoney->send_amount, config('basic.fraction_number')),
                    'fees' => getAmount($sendMoney->fees, config('basic.fraction_number')),
                    'discount' => getAmount($sendMoney->discount ?? 0, config('basic.fraction_number')),
                    'total_payable' => getAmount($sendMoney->totalPay, config('basic.fraction_number')),
                    'recipient_get_amount' => getAmount($sendMoney->recipient_get_amount, config('basic.fraction_number')),
                    'exchange_rate' => getAmount($sendMoney->rate, config('basic.fraction_number')),
                    'send_currency' => $sendMoney->send_curr,
                    'receive_currency' => $sendMoney->receive_curr,
                    'merchant_commission' => getAmount($merchantCom, config('basic.fraction_number')),
                    'payment_status' => $sendMoney->payment_status == 1 ? 'Paid' : 'Pending',
                    'status' => $sendMoney->payment_status == 1 ? 'Processing' : 'Created',
                    'recipient_name' => $sendMoney->recipient_name,
                    'recipient_contact' => $sendMoney->recipient_contact_no,
                    'created_at' => $sendMoney->created_at->toISOString()
                ]
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => false,
                'message' => 'Failed to create remittance: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available countries and services for sending money
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableOptions()
    {
        $user = auth()->user();

        // Merchant authentication and status checks
        if ($user->merchant == 0) {
            return response()->json([
                'status' => false,
                'message' => 'Unauthorized merchant access'
            ], 403);
        }

        if ($user->status == 0) {
            return response()->json([
                'status' => false,
                'message' => 'Merchant is not active!'
            ], 403);
        }

        // Check if merchant has a country assigned
        if (!$user->country_id) {
            return response()->json([
                'status' => false,
                'message' => 'You need to have a country assigned to your merchant account. Please contact support.'
            ], 403);
        }

        try {
            // Get merchant's assigned sending country
            $sendingCountries = Country::select('id', 'name', 'code', 'minimum_amount', 'maximum_amount', 'rate')
                ->where('send_from', 1)
                ->where('status', 1)
                ->where('id', $user->country_id) // Only merchant's assigned country
                ->get();

            // Get all available receiving countries
            $receivingCountries = Country::select('id', 'name', 'code', 'minimum_amount', 'maximum_amount', 'rate')
                ->where('send_to', 1)
                ->where('status', 1)
                ->orderBy('name')
                ->get();

            // Get available services
            $services = Service::select('id', 'name')
                ->where('status', 1)
                ->get();

            return response()->json([
                'status' => true,
                'data' => [
                    'sending_countries' => $sendingCountries,
                    'receiving_countries' => $receivingCountries,
                    'services' => $services,
                    'merchant_country_id' => $user->country_id
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to fetch available options: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate fees and exchange rate for a transaction
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function calculateFees(Request $request)
    {
        $user = auth()->user();

        // Merchant authentication and status checks
        if ($user->merchant == 0) {
            return response()->json([
                'status' => false,
                'message' => 'Unauthorized merchant access'
            ], 403);
        }

        if ($user->status == 0) {
            return response()->json([
                'status' => false,
                'message' => 'Merchant is not active!'
            ], 403);
        }

        // Check if merchant has a country assigned
        if (!$user->country_id) {
            return response()->json([
                'status' => false,
                'message' => 'You need to have a country assigned to your merchant account. Please contact support.'
            ], 403);
        }

        $validator = validator()->make($request->all(), [
            'send_currency_id' => 'required|exists:countries,id',
            'receive_currency_id' => 'required|exists:countries,id',
            'service_id' => 'required|exists:services,id',
            'send_amount' => 'required|numeric|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            // Validate currencies and services
            $sendCountry = Country::where('id', $request->send_currency_id)
                ->where('send_from', 1)
                ->where('status', 1)
                ->first();

            if (!$sendCountry) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid sending country'
                ], 400);
            }

            // Check if merchant can send from this country
            if ($sendCountry->id != $user->country_id) {
                return response()->json([
                    'status' => false,
                    'message' => 'You can only send money from your assigned country'
                ], 403);
            }

            $receiveCountry = Country::where('id', $request->receive_currency_id)
                ->where('send_to', 1)
                ->where('status', 1)
                ->first();

            if (!$receiveCountry) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid receiving country'
                ], 400);
            }

            $service = Service::where('id', $request->service_id)->where('status', 1)->first();
            if (!$service) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid service'
                ], 400);
            }

            // Calculate exchange rate and fees
            $amount = $request->send_amount;
            $rate = $receiveCountry->rate / $sendCountry->rate;
            $fees = getCharge($amount, $receiveCountry->id, $service->id);
            $totalPayable = $amount + $fees;
            $recipientGetAmount = $amount * $rate;

            // Round up (ceiling) the final amount as per requirements
            $totalPayable = ceil($totalPayable);

            // Calculate merchant commission
            $merchantCom = 0;
            if (0 < $fees) {
                $basicCom = config('basic.merchant_commission'); // percent
                $baseCharge = $fees / $sendCountry->rate;
                $merchantCom = ($baseCharge * $basicCom) / 100;
            }

            return response()->json([
                'status' => true,
                'data' => [
                    'send_amount' => getAmount($amount, config('basic.fraction_number')),
                    'fees' => getAmount($fees, config('basic.fraction_number')),
                    'total_payable' => getAmount($totalPayable, config('basic.fraction_number')),
                    'recipient_get_amount' => getAmount($recipientGetAmount, config('basic.fraction_number')),
                    'exchange_rate' => getAmount($rate, config('basic.fraction_number')),
                    'send_currency' => $sendCountry->code,
                    'receive_currency' => $receiveCountry->code,
                    'merchant_commission' => getAmount($merchantCom, config('basic.fraction_number')),
                    'rate_display' => '1 ' . $sendCountry->code . ' = ' . getAmount($rate, config('basic.fraction_number')) . ' ' . $receiveCountry->code
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to calculate fees: ' . $e->getMessage()
            ], 500);
        }
    }
}
