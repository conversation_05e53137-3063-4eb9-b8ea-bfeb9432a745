@extends('admin.layouts.app')
@section('title')
    @lang('Flutterwave Payout Settings')
@endsection

@section('content')
    <div class="row">
        <div class="col-md-6">
            <div class="card card-primary m-0 m-md-4 my-4 m-md-0 shadow">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between mb-3">
                        <div class="col-md-6">
                            <h5 class="card-title  font-weight-bold color-primary">@lang('Flutterwave Payout Settings')</h5>
                        </div>
                    </div>
                    <form action="" method="post"
                          class="needs-validation base-form">
                        @csrf
                        <div class="row my-3">
                            <div class="form-group col-sm-12 col-12">
                                <label>@lang('Flutterwave  PUBLIC KEY')</label>
                                <input type="text" name="PUBLIC_KEY"
                                       value="{{ old('PUBLIC_KEY') ?? $control->PUBLIC_KEY ?? '' }}"
                                       class="form-control ">
                                @error('PUBLIC_KEY')
                                <span class="text-danger">{{ trans($message) }}</span>
                                @enderror
                            </div>

                            <div class="form-group col-sm-12  col-12">
                                <label>@lang('Flutterwave  SECRET KEY')</label>
                                <input type="text" name="SECRET_KEY"
                                       value="{{ old('SECRET_KEY') ?? $control->SECRET_KEY ?? '' }}"
                                       class="form-control ">
                                @error('SECRET_KEY')
                                <span class="text-danger">{{ trans($message) }}</span>
                                @enderror
                            </div>


                            <div class="form-group col-sm-12 col-12">
                                <label>@lang('Flutterwave ENCRYPTION KEY')</label>
                                <input type="text" name="ENCRYPTION_KEY"
                                       value="{{ old('ENCRYPTION_KEY') ?? $control->ENCRYPTION_KEY ?? '' }}"
                                       class="form-control ">
                                @error('ENCRYPTION_KEY')
                                <span class="text-danger">{{ trans($message) }}</span>
                                @enderror
                            </div>

                            <div class="form-group col-sm-12  col-12">
                                <label class="d-block">@lang('Flutterwave Payout Service')</label>
                                <div class="custom-switch-btn">
                                    <input type='hidden' value='1' name='flutterwave_payout_service'>
                                    <input type="checkbox" name="flutterwave_payout_service" class="custom-switch-checkbox"
                                           id="flutterwave_payout_service"
                                           value="0" {{($control->flutterwave_payout_service  == 0) ? 'checked' : ''}} >
                                    <label class="custom-switch-checkbox-label" for="flutterwave_payout_service">
                                        <span class="custom-switch-checkbox-inner2"></span>
                                        <span class="custom-switch-checkbox-switch"></span>
                                    </label>
                                </div>
                            </div>

                        </div>

                        <button type="submit"
                                class="btn waves-effect waves-light btn-rounded btn-primary btn-block mt-3"><span><i
                                    class="fas fa-save pr-2"></i> @lang('Save Changes')</span></button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card card-primary m-0 m-md-4 my-4 m-md-0 shadow">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between mb-3">
                        <div class="col-md-12">
                            <h5 class="card-title  font-weight-bold color-primary">@lang('Flutterwave Automated Transfers By Admin')</h5>
                        </div>
                    </div>

                    <p>
                        After getting payments from users/customers, you can send money automatically to recipients from your account.
                        You can make transfers (also called payouts)  from your Flutterwave available balance. You can transfer to a bank account, a mobile money account,  another Flutterwave account.

                        <br>
                        <br>

                        This document explains a few things you need to know about transfers in general. You should read this first, and then you can view examples for specific types of transfers on their guide pages:
                        <strong>Bank accounts</strong>,
                        <strong>Mobile money</strong>,
                        <strong>Cash pickup</strong>,

                    </p>


                    <div class="bd-callout bd-callout-warning m-0 m-md-4 my-4 m-md-0 ">
                         You can transfer money to the following countries with Flutterwave:
                        <br>
                        <br>

                        Nigeria, Ghana, Kenya, Uganda, Tanzania, South Africa, Zambia, Cameroon, Ivory Coast, Sierra Leone, Burkina Faso, Guinea-Bissau, Mali, Senegal, Rwanda, Tunisia, and Guinea Conakry.


                    </div>

                    <p>

                    </p>
                </div>
            </div>
        </div>
    </div>


@endsection

@push('js')
    <script>
        'use strict';
        $("#update_time_coin_market_cap").select2({
            selectOnClose: true,
            width: '100%'
        })
        $("#update_time_currency_layer").select2({
            selectOnClose: true,
            width: '100%'
        })
    </script>
@endpush

