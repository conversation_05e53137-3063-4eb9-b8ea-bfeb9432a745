<div class="row">
    @if(isset($exactServices['ussd']))
        @php
            $exactServices['ussd']["account_bank"]['value']  = (optional(@$sendMoney->provider)->bank_code)??'';
            $exactServices['ussd']["amount"]['value'] = round($sendMoney->recipient_get_amount,0);
            $exactServices['ussd']["currency"]['value'] = @$sendMoney->receive_curr;
            $exactServices['ussd']["email"]['value'] = @$sendMoney->recipient_email;
            $exactServices['ussd']["phone_number"]['value'] = clean(@$sendMoney->recipient_contact_no);
            $exactServices['ussd']["fullname"]['value'] = @$sendMoney->user_information->BeneficiaryName->field_name ??$sendMoney->recipient_name;
        @endphp
    @endif

    @if(isset($exactServices['debit_ng_account']))
        @php
            $exactServices['debit_ng_account']["amount"]['value'] = round($sendMoney->recipient_get_amount,0);
            $exactServices['debit_ng_account']["account_bank"]['value']  = (optional(@$sendMoney->provider)->bank_code)??'';
            $exactServices['debit_ng_account']["account_number"]['value'] = @$sendMoney->user_information->AccountNumber->field_name;
            $exactServices['debit_ng_account']["currency"]['value'] = @$sendMoney->receive_curr;
            $exactServices['debit_ng_account']["email"]['value'] = @$sendMoney->recipient_email;
            $exactServices['debit_ng_account']["phone_number"]['value'] = clean(@$sendMoney->recipient_contact_no);
            $exactServices['debit_ng_account']["fullname"]['value'] = @$sendMoney->user_information->BeneficiaryName->field_name ??$sendMoney->recipient_name;
        @endphp
    @endif


    @php
        $banks = [];
    @endphp

    @if(isset($exactServices['transfers']))
        @php
            $exactServices['transfers']["account_bank"]['value']  = (optional(@$sendMoney->provider)->bank_code)??'';
            $exactServices['transfers']["account_number"]['value'] = @$sendMoney->user_information->AccountNumber->field_name;
            $exactServices['transfers']["amount"]['value'] = round($sendMoney->recipient_get_amount,0);
            $exactServices['transfers']["narration"]['value'] = @$sendMoney->user_information->BeneficiaryName->field_name ??$sendMoney->recipient_name;
            $exactServices['transfers']["currency"]['value'] = @$sendMoney->receive_curr;
            $exactServices['transfers']["debit_currency"]['value'] = @$sendMoney->receive_curr;
            $exactServices['transfers']["phone_number"]['value'] = clean(@$sendMoney->recipient_contact_no);

            $banks['account_number'] = @$sendMoney->user_information->AccountNumber->field_name;
            $banks['account_bank'] =  (optional(@$sendMoney->provider)->bank_code)??'';
        @endphp

    @endif

    @if(isset($exactServices['mobile_money_ghana']))
        @php
            $exactServices['mobile_money_ghana']["amount"]['value'] = round($sendMoney->recipient_get_amount,0);
            $exactServices['mobile_money_ghana']["currency"]['value'] = @$sendMoney->receive_curr;
            $exactServices['mobile_money_ghana']["voucher"]['value'] = @$sendMoney->invoice;
            $exactServices['mobile_money_ghana']["email"]['value'] = @$sendMoney->recipient_email;
            $exactServices['mobile_money_ghana']["phone_number"]['value'] = clean(@$sendMoney->recipient_contact_no);
            $exactServices['mobile_money_ghana']["fullname"]['value'] = @$sendMoney->user_information->BeneficiaryName->field_name ??$sendMoney->recipient_name;
        @endphp
    @endif
    @if(isset($exactServices['mobile_money_zambia']))
        @php
            $exactServices['mobile_money_zambia']["amount"]['value'] = round($sendMoney->recipient_get_amount,0);
            $exactServices['mobile_money_zambia']["currency"]['value'] = @$sendMoney->receive_curr;
            $exactServices['mobile_money_zambia']["email"]['value'] = @$sendMoney->recipient_email;
            $exactServices['mobile_money_zambia']["phone_number"]['value'] = clean(@$sendMoney->recipient_contact_no);
        @endphp
    @endif


    @if(isset($exactServices['mobile_money_uganda']))
        @php
            $exactServices['mobile_money_uganda']["amount"]['value'] = round($sendMoney->recipient_get_amount,0);
            $exactServices['mobile_money_uganda']["currency"]['value'] = @$sendMoney->receive_curr;
            $exactServices['mobile_money_uganda']["email"]['value'] = @$sendMoney->recipient_email;
            $exactServices['mobile_money_uganda']["phone_number"]['value'] = clean(@$sendMoney->recipient_contact_no);
        @endphp
    @endif

    @if(isset($exactServices['mobile_money_rwanda']))
        @php
            $exactServices['mobile_money_rwanda']["amount"]['value'] = round($sendMoney->recipient_get_amount,0);
            $exactServices['mobile_money_rwanda']["currency"]['value'] = @$sendMoney->receive_curr;
            $exactServices['mobile_money_rwanda']["email"]['value'] = @$sendMoney->recipient_email;
            $exactServices['mobile_money_rwanda']["phone_number"]['value'] = clean(@$sendMoney->recipient_contact_no);
            $exactServices['mobile_money_rwanda']["fullname"]['value'] = @$sendMoney->user_information->BeneficiaryName->field_name ??$sendMoney->recipient_name;
        @endphp
    @endif

    @if(isset($exactServices['debit_uk_account']))
        @php
            $exactServices['debit_uk_account']["account_bank"]['value']  = (optional(@$sendMoney->provider)->bank_code)??'';
            $exactServices['debit_uk_account']["account_number"]['value'] = @$sendMoney->user_information->AccountNumber->field_name ??'';
            $exactServices['debit_uk_account']["amount"]['value'] = round($sendMoney->recipient_get_amount,0);
            $exactServices['debit_uk_account']["currency"]['value'] = @$sendMoney->receive_curr;
            $exactServices['debit_uk_account']["email"]['value'] = @$sendMoney->recipient_email;
            $exactServices['debit_uk_account']["phone_number"]['value'] = clean(@$sendMoney->recipient_contact_no);
            $exactServices['debit_uk_account']["fullname"]['value'] = @$sendMoney->user_information->BeneficiaryName->field_name ??$sendMoney->recipient_name;
        @endphp
    @endif


    @if(isset($exactServices['mpesa']))
        @php
            $exactServices['mpesa']["amount"]['value'] = round($sendMoney->recipient_get_amount,0);
            $exactServices['mpesa']["currency"]['value'] = @$sendMoney->receive_curr;
            $exactServices['mpesa']["email"]['value'] = @$sendMoney->recipient_email;
            $exactServices['mpesa']["phone_number"]['value'] = clean(@$sendMoney->recipient_contact_no);
            $exactServices['mpesa']["fullname"]['value'] = @$sendMoney->user_information->BeneficiaryName->field_name ??$sendMoney->recipient_name;
        @endphp
    @endif
</div>
