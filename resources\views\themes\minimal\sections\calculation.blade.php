@if(isset($templates['calculation'][0]) && $calculation = $templates['calculation'][0])
    <section id="home-banner" style="background-image: linear-gradient(to right, {{hex2rgba(colors()->base_color)}}, {{hex2rgba(colors()->secondary_alternative_color,0.9)}} ), url( {{getFile(config('location.template.path').@$calculation->templateMedia()->image)}});">
        <div class="container h-100">
            <div class="row align-items-center h-100" id="basicCalcInfo" v-cloak>
                <div class="col-xl-7 col-lg-6">
                    <div class="content-wrapper">
                        <div class="banner-heading">
                            <h1>@lang(@$calculation->description->title)</h1>
                        </div>
                        <div class="paragraph text-white">
                            <p>@lang(@$calculation->description->short_description)</p>
                        </div>
                        <div class="get-strated">
                            <a href="{{@$calculation->templateMedia()->button_link}}" class="anim-button">
                                <span class="layer1">@lang($calculation->description->button_name)</span>
                                <span class="layer2"></span>
                            </a>
                        </div>
                    </div>
                </div>


                <div class="col-xl-5 col-lg-6">
                    <div class="calculation-box text-center" style="background: {{hex2rgba(colors()->hero_calculator_bg, 0.8)}}">
                        <div class="mt-5 text-left">
                            <div class="input-box">
                                <label for="" class="text-white">@lang('You Send')</label>
                                <div class="input-group">
                                    <input id="send-input" class="form-control you__send" v-model="send_amount" @change="getValue"
                                           @keypress="onlyNumber"
                                           placeholder="0.00" type="text"/>
                                    <button id="currencySenderBtn" type="button">
                                        <img :src="sendFrom.flag" alt="" />
                                        @{{sendFrom.code}}
                                    </button>
                                </div>
                                <div class="error-massage" v-if="send_amount < (sendFrom.minimum_amount - 0)">
                                    <span>{{trans('The smallest amount you can send is')}} @{{sendFrom.minimum_amount}} @{{sendFrom.code}}</span>
                                </div>
                                <div class="error-massage" v-if="send_amount > (sendFrom.maximum_amount - 0)">
                                    <span>{{trans('The maximum amount you can send is')}} @{{sendFrom.maximum_amount}} @{{sendFrom.code}}</span>
                                </div>
                            </div>

                            <div class="input-box mt-4">
                                <label for="" class="text-white">@lang('Recipient gets')</label>
                                <div class="input-group">
                                    <input type="text" class="form-control recepient__gets" placeholder="0.00" id="get-input" v-model="get_amount" @change="sendValue"
                                           @keypress="onlyNumber" />
                                    <button id="currencyReceiverBtn" type="button">
                                        <img
                                            :src="receiveFrom.flag"
                                            alt=""
                                        />
                                        @{{receiveFrom.code}}
                                    </button>
                                </div>
                            </div>
                            <div class="">
                                <div class="charges mt-4 text-white">
                                    <p><i class="fas fa-question"></i> <span class="mr-1">@{{  rate }} @{{receiveFrom.code}}</span> @lang(' exchange rate')</p>
                                    <p><i class="fas fa-minus"></i> <span>@lang("Fee Depend on your service")</span></p>
                                </div>
                            </div>
                            <div class="my-4">
                                <button class="btn-custom w-100 continue__btn" type="button" @click="goNext" 
                                        :disabled="send_amount < (sendFrom.minimum_amount - 0) || (send_amount > (sendFrom.maximum_amount - 0))">
                                    {{trans('Continue')}}
                                </button>
                            </div>
                        </div>

                        <div id="currencySenderModal" class="modal fade p-0" role="dialog">
                            <div class="modal-dialog">
                                <!-- Modal content-->
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">@lang("You send")</h5>
                                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                                    </div>
                                    <div class="modal-body">
                                        <ul>
                                            <li v-for="item in senderCurrencies">
                                                <a href="javascript:void(0)" @click="changeSender(item.id)" :class="(sendFrom.id ==item.id)?'selected':''">
                                                    <img
                                                        :src="item.flag"
                                                        alt=""
                                                    />
                                                    <p class="mb-0">
                                                        <span class="country">@{{ item.code }}</span>
                                                        <span class="currency">@{{ item.name }}</span>
                                                        <i class="fas fa-check-circle"></i>
                                                    </p>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div id="currencyReceiverModal" class="modal fade p-0" role="dialog">
                            <div class="modal-dialog">
                                <!-- Modal content-->
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">@lang("Recipient gets")</h5>
                                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                                    </div>
                                    <div class="modal-body">
                                        <ul>
                                            <li v-for="item in receiverCurrencies">
                                                <a href="javascript:void(0)" @click="changeReceive(item.id)"  :class="(receiveFrom.id ==item.id)?'selected':''">
                                                    <img
                                                        :src="item.flag"
                                                        alt=""
                                                    />
                                                    <p class="mb-0">
                                                        <span class="country">@{{ item.code }}</span>
                                                        <span class="currency">@{{ item.name }}</span>
                                                        <i class="fas fa-check-circle"></i>
                                                    </p>
                                                </a>
                                            </li>

                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>



    @push('script')

        <script>
            'use strict';

            $(document).on("click", "#currencySenderBtn", function () {
                $("#currencySenderModal").modal("show");

                $(".blue").addClass("after_modal_appended");

                //appending modal background inside the blue div
                $(".modal-backdrop").appendTo(".blue");

                //remove the padding right and modal-open class from the body tag which bootstrap adds when a modal is shown

                $("body").removeClass("modal-open");
                $("body").css("padding-right", "");
            });

            $(document).on("click", "#currencyReceiverBtn", function () {
                $("#currencyReceiverModal").modal("show");

                $(".blue").addClass("after_modal_appended");

                //appending modal background inside the blue div
                $(".modal-backdrop").appendTo(".blue");

                //remove the padding right and modal-open class from the body tag which bootstrap adds when a modal is shown

                $("body").removeClass("modal-open");
                $("body").css("padding-right", "");
            });


            new Vue({
                el: "#basicCalcInfo",
                data: {
                    senderCurrencies: [],
                    receiverCurrencies: [],
                    sendFrom: {},
                    receiveFrom: {},
                    send_amount: '',
                    get_amount: '',
                    rate: '',
                },
                beforeMount() {
                    this.currencyList();
                    if (localStorage.getItem('resource') != null) {
                        this.sendFrom = JSON.parse(localStorage.getItem('resource'))
                    }

                },
                mounted() {
                    let self = this;


                },
                methods: {
                    currencyList() {
                        axios.get('{{route('currencyList')}}')
                            .then(res => {
                                this.senderCurrencies = res.data.senderCurrencies
                                this.receiverCurrencies = res.data.receiverCurrencies
                                if (0 < this.senderCurrencies.length) {
                                    this.sendFrom = this.senderCurrencies[0]
                                }
                                if (0 < this.receiverCurrencies.length) {
                                    this.receiveFrom = this.receiverCurrencies[0]
                                }
                                this.getRate()
                            })
                            .catch(err => {
                            });
                    },

                    changeSender(id){
                        $("#currencySenderModal").modal("hide");

                        var self = this;
                        var arr = self.senderCurrencies;
                        const result = arr.find((obj, index) => {
                            if (obj.id == id) {
                                return true
                            }
                        });
                        this.sendFrom = result

                        console.log(this.sendFrom)

                        this.getRate();
                        this.getValue();
                    },


                    changeReceive(id){
                        $("#currencyReceiverModal").modal("hide");

                        var self = this;
                        var arr = self.receiverCurrencies;
                        const result = arr.find((obj, index) => {
                            if (obj.id == id) {
                                return true
                            }
                        });
                        this.receiveFrom = result
                        this.getRate()

                        this.getValue();
                    },
                    onlyNumber($event) {
                        //console.log($event.keyCode); //keyCodes value
                        let keyCode = ($event.keyCode ? $event.keyCode : $event.which);
                        if ((keyCode < 48 || keyCode > 57) && keyCode !== 46) { // 46 is dot
                            $event.preventDefault();
                        }
                    },
                    formatState(state) {
                        if (!state.id) {
                            return state.text;
                        }
                        var image = $(state.element).data('image');
                        var $state = $('<span style="font-size: 16px; margin-left: 8px;"><img src="' + image + '" style="width: 20px;height: 15px;margin-right: 12px;"> <span class="country-code-name">' + state.text + '</span> </span>');
                        return $state;
                    },
                    getRate() {
                        var setRate = this.receiveFrom.rate / this.sendFrom.rate;
                        this.rate = setRate.toFixed(2);
                        return setRate;
                    },
                    getValue() {
                        this.get_amount = (this.send_amount * this.getRate()).toFixed(2)
                        Math.abs(this.get_amount)
                    },
                    sendValue() {
                        this.send_amount = (this.get_amount / this.getRate()).toFixed(2)
                        Math.abs(this.send_amount)
                    },
                    goNext() {
                        var $url = '{{ route("toCountry", "country:slug") }}';
                        $url = $url.replace('country:slug', this.receiveFrom.slug);
                        localStorage.setItem('send_amount', this.send_amount);
                        localStorage.setItem('sendFrom', JSON.stringify(this.sendFrom));
                        localStorage.setItem('receiveFrom', JSON.stringify(this.receiveFrom));

                        localStorage.setItem('sendSelectId', this.sendFrom.id);
                        localStorage.setItem('sendSelectFlag', this.sendFrom.flag);
                        localStorage.setItem('sendSelectName', this.sendFrom.name);
                        localStorage.setItem('sendSelectCode', this.sendFrom.code);
                        localStorage.setItem('resource', JSON.stringify(this.sendFrom));
                        window.location.href = $url
                    }
                }
            });
        </script>

    @endpush


@endif
