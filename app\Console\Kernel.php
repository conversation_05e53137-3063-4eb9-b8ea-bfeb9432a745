<?php

namespace App\Console;

use App\Console\Commands\BlockIoIPN;
use App\Console\Commands\CreateDomPdfDirectories;
use App\Console\Commands\Cron;
use App\Console\Commands\ExpireUnpaidRemittances;
use App\Models\Gateway;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        BlockIoIPN::class,
        CreateDomPdfDirectories::class,
        ExpireUnpaidRemittances::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')->hourly();
		/*
        $blockIoGateway = Gateway::where(['code' => 'blockio', 'status' => 1])->count();
        if ($blockIoGateway == 1) {
            $schedule->command('blockIo:ipn')->everyThirtyMinutes();
        }
		*/

        $basicControl = basicControl();
        if ($basicControl->currency_layer_auto_update == 1) {
            $schedule->command('fiat-currency:update')->{basicControl()->currency_layer_auto_update_at}();
            $schedule->command('payout-currency:update')
                ->{basicControl()->currency_layer_auto_update_at}();
        }
        if ($basicControl->coin_market_cap_auto_update == 1) {
            $schedule->command('crypto-currency:update')->{basicControl()->coin_market_cap_auto_update_at}();
            $schedule->command('payout-crypto-currency:update')->{basicControl()->coin_market_cap_auto_update_at}();
        }

        // Run every 5 minutes to expire unpaid remittances after 30 minutes
        $schedule->command('remittance:expire-unpaid')->everyFiveMinutes();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
