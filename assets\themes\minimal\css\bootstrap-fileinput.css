/*!
 * <PERSON><PERSON><PERSON> Bootstrap v3.1.3 (http://jasny.github.io/bootstrap)
 * Copyright 2012-2014 <PERSON>
 * Licensed under Apache-2.0 (https://github.com/jasny/bootstrap/blob/master/LICENSE)
 */

.btn-file {
    position: relative;
    overflow: hidden;
    vertical-align: middle;
}
.btn-file > input {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    font-size: 23px;
    cursor: pointer;
    filter: alpha(opacity=0);
    opacity: 0;

    direction: ltr;
}
.fileinput {
    display: inline-block;
    margin-bottom: 9px;
}
.fileinput .form-control {
    display: inline-block;
    padding-top: 7px;
    padding-bottom: 5px;
    margin-bottom: 0;
    vertical-align: middle;
    cursor: text;
}
.fileinput .thumbnail {
    display: inline-block;
    margin-bottom: 5px;
    overflow: hidden;
    text-align: center;
    vertical-align: middle;
}
.fileinput .thumbnail > img {
    max-height: 100%;
}
.fileinput .btn {
    vertical-align: middle;
}
.fileinput-exists .fileinput-new,
.fileinput-new .fileinput-exists {
    display: none;
}
.fileinput-inline .fileinput-controls {
    display: inline;
}
.fileinput-filename {
    display: inline-block;
    overflow: hidden;
    vertical-align: middle;
}
.form-control .fileinput-filename {
    vertical-align: bottom;
}
.fileinput.input-group {
    display: table;
}
.fileinput.input-group > * {
    position: relative;
    z-index: 2;
}
.fileinput.input-group > .btn-file {
    z-index: 1;
}
.fileinput-new.input-group .btn-file,
.fileinput-new .input-group .btn-file {
    border-radius: 0 4px 4px 0;
}
.fileinput-new.input-group .btn-file.btn-xs,
.fileinput-new .input-group .btn-file.btn-xs,
.fileinput-new.input-group .btn-file.btn-sm,
.fileinput-new .input-group .btn-file.btn-sm {
    border-radius: 0 3px 3px 0;
}
.fileinput-new.input-group .btn-file.btn-lg,
.fileinput-new .input-group .btn-file.btn-lg {
    border-radius: 0 6px 6px 0;
}
.form-group.has-warning .fileinput .fileinput-preview {
    color: #8a6d3b;
}
.form-group.has-warning .fileinput .thumbnail {
    border-color: #faebcc;
}
.form-group.has-error .fileinput .fileinput-preview {
    color: #a94442;
}
.form-group.has-error .fileinput .thumbnail {
    border-color: #ebccd1;
}
.form-group.has-success .fileinput .fileinput-preview {
    color: #3c763d;
}
.form-group.has-success .fileinput .thumbnail {
    border-color: #d6e9c6;
}
.input-group-addon:not(:first-child) {
    border-left: 0;
}
