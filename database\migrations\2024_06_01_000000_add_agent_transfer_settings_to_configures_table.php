<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAgentTransferSettingsToConfiguresTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('configures', function (Blueprint $table) {
            $table->decimal('agent_transfer_min_amount', 18, 8)->nullable()->default(10);
            $table->decimal('agent_transfer_max_amount', 18, 8)->nullable()->default(1000);
            $table->decimal('agent_transfer_fixed_fee', 18, 8)->nullable()->default(1);
            $table->decimal('agent_transfer_percentage_fee', 18, 8)->nullable()->default(1);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('configures', function (Blueprint $table) {
            $table->dropColumn('agent_transfer_min_amount');
            $table->dropColumn('agent_transfer_max_amount');
            $table->dropColumn('agent_transfer_fixed_fee');
            $table->dropColumn('agent_transfer_percentage_fee');
        });
    }
}
