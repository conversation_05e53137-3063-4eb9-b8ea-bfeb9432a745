@push('script')
    <script>
        var root = document.querySelector(':root');
        root.style.setProperty('--brand-color', '{{colors()->base_color??'#589ab8'}}');
        root.style.setProperty('--brand-color-alt2', '{{colors()->secondary_alternative_color??'#4db377'}}');
        root.style.setProperty('--button-color', '{{colors()->button_highlighter_color??'#4db377'}}');
        root.style.setProperty('--brand-color-alt', '{{colors()->secondary_color??'#385081'}}');
        root.style.setProperty('--background-color', '{{colors()->background_color??'#ffffff'}}');
        root.style.setProperty('--background-color-alt', '{{colors()->secondary_background_color??'#f2f5f7'}}');

        root.style.setProperty('--title-color', '{{colors()->title_color ??'#37517e'}}');
        root.style.setProperty('--text-color', '{{colors()->text_color ??'#2e4369'}}');
        root.style.setProperty('--natural-color', '{{colors()->natural_color ??'#ffffff'}}');
        root.style.setProperty('--error', '{{colors()->error_color ??'#f21a29'}}');

        root.style.setProperty('--brand-color-alt-dark', '{{colors()->secondary_alternative_color??'#022c63'}}');
        root.style.setProperty('--brand-color-light', '{{colors()->background_alternative_color??'#e6f9f8'}}');
        root.style.setProperty('--border-color', '{{colors()->border_color??'#e7e5e5'}}');
        root.style.setProperty('--secondary', '{{colors()->hero_calculator_bg??'#2c4e5d'}}');
    </script>
@endpush
