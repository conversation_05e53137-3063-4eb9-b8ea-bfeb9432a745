<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SendMoney;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class EarningsReportController extends Controller
{
    /**
     * Display the earnings report
     */
    public function index()
    {
        // Get completed remittances with earnings calculations
        $earnings = SendMoney::completed()
                          ->with(['user', 'payoutMerchant', 'sendingMerchant', 'payment.gateway'])
                          ->orderBy('received_at', 'desc')
                          ->paginate(config('basic.paginate'));

        // Calculate total earnings from all completed remittances
        $totalEarnings = SendMoney::completed()
                                ->selectRaw('SUM(fees - COALESCE(merchant_commission, 0) - COALESCE(merchant_profit, 0)) as total')
                                ->value('total') ?? 0;

        $merchants = User::where('merchant', 1)->select('id', 'firstname', 'lastname', 'username')->get();

        // Get unique payment gateways from completed remittances
        $paymentGateways = SendMoney::completed()
                                  ->whereHas('payment.gateway')
                                  ->with('payment.gateway')
                                  ->get()
                                  ->pluck('payment.gateway.name')
                                  ->unique()
                                  ->filter()
                                  ->values();

        $data = [
            'page_title' => 'Earnings Report',
            'earnings' => $earnings,
            'totalEarnings' => $totalEarnings,
            'merchants' => $merchants,
            'paymentGateways' => $paymentGateways,
        ];

        return view('admin.earnings.index', $data);
    }

    /**
     * Search and filter earnings
     */
    public function search(Request $request)
    {
        $query = SendMoney::completed()
                         ->with(['user', 'payoutMerchant', 'sendingMerchant', 'payment.gateway']);

        // Date range filter
        if ($request->filled('start_date')) {
            $query->where('received_at', '>=', Carbon::parse($request->start_date)->startOfDay());
        }

        if ($request->filled('end_date')) {
            $query->where('received_at', '<=', Carbon::parse($request->end_date)->endOfDay());
        }

        // Merchant filter
        if ($request->filled('merchant_id')) {
            $query->byMerchant($request->merchant_id);
        }

        // Payment gateway filter
        if ($request->filled('payment_gateway')) {
            $query->byGateway($request->payment_gateway);
        }

        // Currency filter
        if ($request->filled('currency')) {
            $query->byCurrency($request->currency);
        }

        $earnings = $query->orderBy('received_at', 'desc')
                         ->paginate(config('basic.paginate'));

        // Calculate filtered total earnings using the same query
        $filteredTotalEarnings = (clone $query)->selectRaw('SUM(fees - COALESCE(merchant_commission, 0) - COALESCE(merchant_profit, 0)) as total')
                                              ->value('total') ?? 0;

        $merchants = User::where('merchant', 1)->select('id', 'firstname', 'lastname', 'username')->get();

        // Get unique payment gateways from completed remittances
        $paymentGateways = SendMoney::completed()
                                  ->whereHas('payment.gateway')
                                  ->with('payment.gateway')
                                  ->get()
                                  ->pluck('payment.gateway.name')
                                  ->unique()
                                  ->filter()
                                  ->values();

        $data = [
            'page_title' => 'Earnings Report',
            'earnings' => $earnings,
            'totalEarnings' => $filteredTotalEarnings,
            'merchants' => $merchants,
            'paymentGateways' => $paymentGateways,
        ];

        return view('admin.earnings.index', $data);
    }

    /**
     * Export earnings to CSV
     */
    public function exportCsv(Request $request)
    {
        $query = SendMoney::completed()
                         ->with(['user', 'payoutMerchant', 'sendingMerchant', 'payment.gateway']);

        // Apply same filters as search
        if ($request->filled('start_date')) {
            $query->where('received_at', '>=', Carbon::parse($request->start_date)->startOfDay());
        }

        if ($request->filled('end_date')) {
            $query->where('received_at', '<=', Carbon::parse($request->end_date)->endOfDay());
        }

        if ($request->filled('merchant_id')) {
            $query->byMerchant($request->merchant_id);
        }

        if ($request->filled('payment_gateway')) {
            $query->byGateway($request->payment_gateway);
        }

        if ($request->filled('currency')) {
            $query->byCurrency($request->currency);
        }

        $earnings = $query->orderBy('received_at', 'desc')->get();
        $totalEarnings = $earnings->sum('net_earnings');

        $filename = 'earnings_report_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($earnings, $totalEarnings) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Date',
                'Remittance ID',
                'Sending Merchant',
                'Receiving Merchant',
                'Total Fees',
                'Sending Merchant Share',
                'Receiving Merchant Share',
                'Net Earnings',
                'Payment Gateway',
                'Send Currency',
                'Receive Currency'
            ]);

            // Data rows
            foreach ($earnings as $earning) {
                fputcsv($file, [
                    $earning->received_at ? $earning->received_at->format('Y-m-d H:i:s') : $earning->created_at->format('Y-m-d H:i:s'),
                    '#' . $earning->invoice,
                    $earning->sendingMerchant ? $earning->sendingMerchant->username : ($earning->user ? $earning->user->username : 'N/A'),
                    $earning->payoutMerchant ? $earning->payoutMerchant->username : 'N/A',
                    $earning->formatted_total_fees,
                    $earning->formatted_sending_merchant_share,
                    $earning->formatted_receiving_merchant_share,
                    $earning->formatted_net_earnings,
                    $earning->payment_gateway ?? 'N/A',
                    $earning->send_curr ?? 'N/A',
                    $earning->receive_curr ?? 'N/A'
                ]);
            }

            // Total row
            fputcsv($file, []);
            fputcsv($file, [
                'TOTAL EARNINGS:',
                '',
                '',
                '',
                '',
                '',
                '',
                getAmount($totalEarnings, config('basic.fraction_number')),
                '',
                '',
                ''
            ]);

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    /**
     * Export earnings to PDF
     */
    public function exportPdf(Request $request)
    {
        $query = SendMoney::completed()
                         ->with(['user', 'payoutMerchant', 'sendingMerchant', 'payment.gateway']);

        // Apply same filters as search
        if ($request->filled('start_date')) {
            $query->where('received_at', '>=', Carbon::parse($request->start_date)->startOfDay());
        }

        if ($request->filled('end_date')) {
            $query->where('received_at', '<=', Carbon::parse($request->end_date)->endOfDay());
        }

        if ($request->filled('merchant_id')) {
            $query->byMerchant($request->merchant_id);
        }

        if ($request->filled('payment_gateway')) {
            $query->byGateway($request->payment_gateway);
        }

        if ($request->filled('currency')) {
            $query->byCurrency($request->currency);
        }

        $earnings = $query->orderBy('received_at', 'desc')->get();
        $totalEarnings = $earnings->sum('net_earnings');

        $data = [
            'earnings' => $earnings,
            'totalEarnings' => $totalEarnings,
            'filters' => $request->all(),
            'generatedAt' => now()->format('Y-m-d H:i:s'),
        ];

        $pdf = Pdf::loadView('admin.earnings.pdf', $data);

        $filename = 'earnings_report_' . date('Y-m-d_H-i-s') . '.pdf';

        return $pdf->download($filename);
    }
}
