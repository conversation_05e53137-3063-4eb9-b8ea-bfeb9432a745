<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AgentTransfer extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    /**
     * Get the sender of the transfer.
     */
    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    /**
     * Get the receiver of the transfer.
     */
    public function receiver()
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }

    /**
     * Get the transaction associated with this transfer.
     */
    public function transaction()
    {
        return $this->morphOne(Transaction::class, 'transactional');
    }
}
